import ClipboardContent = require("./clipboard_content");

declare class ClipboardServiceProxy {
    constructor();
    getContent: () => Promise<ClipboardContent>;
    setContent: (content: ClipboardContent) => Promise<boolean>;
    addChangedListener: (listener: () => void,
            callback: (err: Object, ret?: boolean) => void) => void;
    removeChangedListener: (listener: () => void,
            callback: (err: Object, ret?: boolean) => void) => void;
}

export = ClipboardServiceProxy;
