import EventEmitter = require("../core/EventEmitter");
/**
 * FileWatcher handle the watch on file or directory
 * @extends yunos.core.EventEmitter
 * @private
 */
declare class FileWatcher extends EventEmitter {
    private _path;
    private _events;
    private _watcher;
    public constructor(path: string, eventsIn: number);
    private startWatching(): void;
    private stopWatching(): void;
    public static EVENT_ACCESS: int;
    public static EVENT_MODIFY: int;
    public static EVENT_ATTRIB: int;
    public static EVENT_CLOSE_WRITE: int;
    public static EVENT_CLOSE_NOWRITE: int;
    public static EVENT_OPEN: int;
    public static EVENT_MOVED_FROM: int;
    public static EVENT_MOVED_TO: int;
    public static EVENT_CREATE: int;
    public static EVENT_DELETE: int;
    public static EVENT_DELETE_SELF: int;
    public static EVENT_MOVE_SELF: int;
    public static EVENT_ALL: int;
}
export = FileWatcher;
