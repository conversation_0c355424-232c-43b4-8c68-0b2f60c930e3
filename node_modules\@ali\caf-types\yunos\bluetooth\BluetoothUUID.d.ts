/**
 * See Bluetooth Assigned Numbers document - SDP section, to get the values of
 * UUIDs for the various services.
 * The following 128 bit values are calculated as: uuid * 2^96 + BASE_UUID
 * @permission BLUETOOTH.permission.yunos.com
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @public
 * @since 5
 */
declare class BluetoothUUID {
    /**
     * <p>Check whether target UUID array were included in source UUID arrays.</p>
     * @param {string[]}  array - The source UUID arrays.
     * @param {string[]}  targets - The target UUID arrays.
     * @public
     * @since 5
     */
    public static containsAnyUUID(array: string[], targets: string[]): boolean;
    /**
     * <p>UUID for Bluetooth Service Class AudioSink.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly AudioSink = "0000110b-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class AudioSource.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly AudioSource = "0000110a-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class AdvancedAudioDistribution.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly AdvAudioDist = "0000110d-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Headset.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly HSP = "00001108-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Headset � Audio Gateway (AG).</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly HSP_AG = "00001112-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Handsfree.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly Handsfree = "0000111e-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class HandsfreeAudioGateway.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly Handsfree_AG = "0000111f-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Handsfree.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly AvrcpController = "0000110e-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class A/V_RemoteControlTarget.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly AvrcpTarget = "0000110c-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class OBEXObjectPush.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly ObexObjectPush = "00001105-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class HumanInterfaceDeviceService.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly HID = "00001124-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class HOGP.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly HOGP = "00001812-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class PANU.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly PANU = "00001115-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class NAP.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly NAP = "00001116-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class BNEP.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly BNEP = "0000000f-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Phonebook Access � PCE.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly PBAP_PCE = "0000112e-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Phonebook Access � PSE.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly PBAP_PSE = "0000112f-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Message Access Profile.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly MAP = "00001134-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly MNS = "00001133-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID for Bluetooth Service Class Message Notification Server.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly MAS = "00001132-0000-1000-8000-00805f9b34fb";
    /**
     * <p>UUID base string.</p>
     * @constant {string}
     * @public
     * @since 5
     */
    public static readonly BASE = "00000000-0000-1000-8000-00805f9b34fb";
}
export = BluetoothUUID;
