import EventEmitter = require("../core/EventEmitter");
import Context = require("./Context");
import View = require("../ui/view/View");
import Model = require("./Model");
import { AnimationType, Events } from "./core/define";
/**
 * <p>Presenter helps you to seperate presentation logic from view and model, it acts as a "middle-man" between those two layers.</p>
 * @example
 *
 * class MyPresenter extends Presenter {
 *
 *     get events() {
 *         return {
 *             "detail-entry": {
 *                 "tap": function() {
 *                     // noop
 *                 }
 *             }
 *         }
 *     }
 *
 *     onCreate() {
 *         this.adapter = new HomeAdapter();
 *         this.model = new HomeModel();
 *     }
 *
 *     onViewAttached(listView) {
 *         this.adapter.data = this.model.fetch();
 *         listView.adapter = this.adapter;
 *     }
 *
 *     onViewDetached() {
 *         listView.adapter = null;
 *     }
 *
 *     onDestroy() {
 *         this.adapter.destroy();
 *         this.model.destroy();
 *     }
 * }
 *
 * class MyPage extends Page {
 *     onCreate() {
 *         let home = new HomePresenter();
 *
 *         home.attachView("home.xml");
 *
 *         let rootView = home.view;
 *         let window = this.window;
 *
 *         rootView.width = window.width;
 *         rootView.height = window.height;
 *
 *         window.addChild(rootView);
 *     }
 * }
 *
 * @extends yunos.core.EventEmitter
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @public
 * @since 3
 *
 */
declare class Presenter extends EventEmitter {
    public _releaseStrategy: number;
    public isSyncing: boolean;
    public _openEnterAnimationOnce: string;
    public _closeExitAnimationOnce: string;
    public _isShown: boolean;
    protected _children: Presenter[];
    protected _printLifeCycleActivity: boolean;
    private _view;
    private _model;
    private _id;
    private _parent;
    private _helper;
    private _animationConfigUpdated;
    private _context;
    private _showAnimation;
    private _hideAnimaton;
    private _openEnterAnimationType;
    private _viewCreatedByPresenter;
    private _modelCreatedByRouter;
    private _viewState;
    private _hideAttachViewAfterPresenterHide;
    private _openEnter;
    private _openEnterAnimation;
    private _closeExitAnimation;
    private _suspendToBackgroundAnimation;
    private _resumeFromBackgroundAnimation;
    private _autoBindedEvents;
    private _enableLayerForView;
    private _description;
    /**
     * <p>Create a presenter</p>
     */
    public constructor(data?: Context);
    protected initializePresenter(): void;
    private updateAnimationConfig;
    /**
     * <p>You may override this method to implement customized initilizing when creating a Base Presenter your own.</p>
     * @protected
     * @since 4
     *
     */
    protected create(): void;
    /**
     * <p>This method will be invoked when this presetner instance is created.</p>
     * @public
     * @since 3
     *
     */
    public onCreate(): void;
    /**
     * <p>This method will be invoked when the context updated.</p>
     * @public
     * @since 3
     *
     */
    public onContextUpdate(): void;
    private _destroy;
    /**
     * <p>Destroy this presenter instance.</p>
     * <p>The attached view will be detached and all event handlers defined in events attrbute will be removed if attached view exists</p>
     * <p>Which means the two events(viewwilldetach、viewdetached) and lifecycle methods(onViewWillAttach、onViewDetached) will be emitted and invoked.</p>
     * <p>Then the destroy event will be emitted and onDestroy lifecycle method will be invoked and all children will be removed.</p>
     * @fires yunos.appmodel.Presenter#destroy
     * @public
     * @since 3
     *
     */
    public destroy(): void;
    /**
     * <p>This method will be invoked when this presetner instance is destroyed.</p>
     * @public
     * @since 3
     *
     */
    public onDestroy(): void;
    /**
     * <p>Attach a view to this presetner instance.</p>
     * <p>Event handles defined in events attribute will be register to this attached view automatically.</p>
     * <p>The lifecycle methods onViewWillAttach and onViewAttached will be invoked, the related events will be emitted.</p>
     * @param {yunos.ui.view.View|string} view - to be attached view instance or a markup files' name.
     * @param {*} model - model which will be bound to the view.
     * @fires yunos.appmodel.Presenter#viewwillattach
     * @fires yunos.appmodel.Presenter#viewattached
     * @throws {Error} If this presenter has attached a view.
     * @throws {TypeError} If view is not instance of View or a markup file's name.
     * @public
     * @since 3
     *
     */
    public attachView(view: View | string, model?: Object): void;
    /**
     * <p>Create a view instance from markup file.</p>
     * @param {string} file - markup file
     * @param {*} [model] - model
     * @return {yunos.ui.view.View} view instance.
     * @public
     * @since 4
     *
     */
    public createViewFromMarkup(file: string, model?: Object): View;
    private _bindEvents;
    /**
     * <p>This lifecycle method will be invoked when calling attachView method.</p>
     * <p><code>this.view</code> will still be <code>null</code>.</p>
     * @param {yunos.ui.view.View} view - will be attached view.
     * @public
     * @since 3
     *
     */
    public onViewWillAttach(view: View): void;
    /**
     * <p>This lifecycle method will be invoked when calling attachView method and after onViewWillAttach invoked.</p>
     * <p><code>this.view</code> will still be <code>null</code>.</p>
     * @param {yunos.ui.view.View} view - will be attached view.
     * @public
     * @since 3
     *
     */
    public onViewAttached(view: View): void;
    /**
     * <p>Detach view from this presetner instance.</p>
     * <p>Event handles defined in events attribute which registered to the attached view will be removed automatically.</p>
     * <p>The lifecycle methods onViewWillDetach and onViewDetached will be invoked, the related events will be emitted.</p>
     * @fires yunos.appmodel.Presenter#viewwilldetach
     * @fires yunos.appmodel.Presenter#viewdetached
     * @throws {Error} If this presenter hasn't attached a view.
     * @public
     * @since 3
     *
     */
    public detachView(): void;
    private _unbindEvents;
    /**
     * <p>This lifecycle method will be invoked when calling detachView method.</p>
     * <p><code>this.view</code> still holds the attached view.</p>
     * @param {yunos.ui.view.View} view - will be detached view.
     * @public
     * @since 3
     *
     */
    public onViewWillDetach(view: View): void;
    /**
     * <p>This lifecycle method will be invoked when calling detachView method and after onViewWillDetach invoked.</p>
     * <p><code>this.view</code> will be null.</p>
     * @param {yunos.ui.view.View} view - the detached view.
     * @public
     * @since 3
     *
     */
    public onViewDetached(view: View): void;
    private addChild(presenter: Presenter): void;
    private onChildWillAdd;
    private onChildAdded;
    private removeChild(presenter: Presenter, purge?: boolean): void;
    public removeAllChildren(): void;
    private onChildWillRemove;
    private onChildRemoved;
    private insertChild;
    private replaceChild;
    private _readyToShow;
    /**
     * <p>This lifecycle method will be invoked once the presenter is showing.</p>
     * @protected
     * @since 3
     *
     */
    protected onShow(): void;
    private _readyToHide;
    /**
     * <p>This lifecycle method will be invoked when the presenter hided.</p>
     * @protected
     * @since 3
     *
     */
    protected onHide(): void;
    private _saveViewState;
    private _restoreViewState;
    public back(param: Object): boolean;
    /**
     * <p>This lifecycle method will be invoked before the presenter popped from the stack.</p>
     * <p>You may return a true value to prevent the pop action.</p>
     * @protected
     * @since 3
     *
     */
    protected onBack(param: Object): boolean;
    public onHomeKey(): void;
    private stopPresenter;
    private tryEmit(event: string, method: string, ...args: Object[]): View;
    public tryEmitWithLog(event: string, method: string, ...args: Object[]): View;
    /**
     * <p>Defines unique identifier for this presenter.</p>
     * @name yunos.appmodel.Presenter#id
     * @type {string}
     * @throws {TypeError} If parameter is not a string.
     * @throws {Error} If id duplicated with another one.
     * @throws {Error} If parameter is empty string.
     * @public
     * @since 3
     *
     */
    public id: string;
    /**
     * <p>Defines the description for this presenter.</p>
     * @name yunos.appmodel.Presenter#description
     * @type {string}
     * @public
     * @since 3
     *
     */
    public description: string;
    /**
     * <p>The array of all children in this presenter instanc.</p>
     * @name yunos.appmodel.Presenter#children
     * @type {yunos.appmodel.Presenter[]}
     * @default []
     * @readonly
     * @private
     */
    private readonly children: Presenter[];
    /**
     * <p>The parent of this presenter.</p>
     * @name yunos.appmodel.Presenter#parent
     * @type {yunos.appmodel.Presenter|null}
     * @default null
     * @readonly
     * @private
     */
    private readonly parent: Presenter;
    /**
     * <p>The attached view.</p>
     * @name yunos.appmodel.Presenter#view
     * @type {yunos.ui.view.View|null}
     * @default null
     * @readonly
     * @protected
     * @since 3
     *
     */
    protected readonly view: View;
    /**
     * <p>The attached model.</p>
     * @name yunos.appmodel.Presenter#model
     * @type {yunos.appmodel.Model|null}
     * @default null
     * @public
     * @since 3
     *
     */
    public model: Model;
    /**
     * <p>Events config.</p>
     * @example
     * class MyPresenter extends Presenter {
     *     get events() {
     *         return {
     *             // view's id
     *             "home-entry": {
     *                 // event name and it's handler
     *                 "tap": function() {
     *                     // noop
     *                 }
     *             }
     *         }
     *     }
     * }
     * @name yunos.appmodel.Presenter#events
     * @type {Object}
     * @default null
     * @readonly
     * @protected
     * @since 3
     *
     */
    protected readonly events: Events;
    /**
     * <p>Hold the context of presenter.</p>
     * @name yunos.appmodel.Presenter#context
     * @type {yunos.appmodel.Context}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly context: Context;
    public enableLayerForView: boolean;
    /**
     * <p>Set openEnterAnimation.</p>
     * @name yunos.appmodel.Presenter#openEnterAnimation
     * @type {string | Animation}
     * @public
     * @since 4
     *
     */
    public openEnterAnimation: AnimationType;
    /**
     * <p>Set closeExitAnimation.</p>
     * @name yunos.appmodel.Presenter#closeExitAnimation
     * @type {string | Animation}
     * @public
     * @since 4
     *
     */
    public closeExitAnimation: AnimationType;
    /**
     * <p>Set resumeFromBackgroundAnimation.</p>
     * @name yunos.appmodel.Presenter#resumeFromBackgroundAnimation
     * @type {string | Animation}
     * @public
     * @since 4
     *
     */
    public resumeFromBackgroundAnimation: AnimationType;
    /**
     * <p>Set suspendToBackgroundAnimation.</p>
     * @name yunos.appmodel.Presenter#suspendToBackgroundAnimation
     * @type {string | Animation}
     * @public
     * @since 4
     *
     */
    public suspendToBackgroundAnimation: AnimationType;
    public hideAttachViewAfterPresenterHide: boolean;
    /**
     * <p>Enum for Presenter SuspendToBackgroundAnimation.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly SuspendToBackgroundAnimation: {
        /**
         * None
         * @public
         * @since 4
         *
         */
        None: string;
        /**
         * SlideToLeft
         * @public
         * @since 4
         *
         */
        SlideToLeft: string;
        /**
         * SlideToRight
         * @public
         * @since 4
         *
         */
        SlideToRight: string;
        /**
         * SlideToTop
         * @public
         * @since 4
         *
         */
        SlideToTop: string;
        /**
         * SlideToBottom
         * @public
         * @since 4
         *
         */
        SlideToBottom: string;
        /**
         * ZoomOut
         * @public
         * @since 4
         *
         */
        ZoomOut: string;
        /**
         * FadeOut
         * @public
         * @since 4
         *
         */
        FadeOut: string;
    };
    /**
     * <p>Enum for Presenter CloseExitAnimation.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly CloseExitAnimation: {
        /**
         * None
         * @public
         * @since 4
         *
         */
        None: string;
        /**
         * SlideToLeft
         * @public
         * @since 4
         *
         */
        SlideToLeft: string;
        /**
         * SlideToRight
         * @public
         * @since 4
         *
         */
        SlideToRight: string;
        /**
         * SlideToTop
         * @public
         * @since 4
         *
         */
        SlideToTop: string;
        /**
         * SlideToBottom
         * @public
         * @since 4
         *
         */
        SlideToBottom: string;
        /**
         * ZoomOut
         * @public
         * @since 4
         *
         */
        ZoomOut: string;
        /**
         * FadeOut
         * @public
         * @since 4
         *
         */
        FadeOut: string;
    };
    /**
     * <p>Enum for Presenter ResumeFromBackgroundAnimation.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly ResumeFromBackgroundAnimation: {
        /**
         * None
         * @public
         * @since 4
         *
         */
        None: string;
        /**
         * SlideFromLeft
         * @public
         * @since 4
         *
         */
        SlideFromLeft: string;
        /**
         * SlideFromRight
         * @public
         * @since 4
         *
         */
        SlideFromRight: string;
        /**
         * SlideFromTop
         * @public
         * @since 4
         *
         */
        SlideFromTop: string;
        /**
         * SlideFromBottom
         * @public
         * @since 4
         *
         */
        SlideFromBottom: string;
        /**
         * ZoomIn
         * @public
         * @since 4
         *
         */
        ZoomIn: string;
        /**
         * FadeIn
         * @public
         * @since 4
         *
         */
        FadeIn: string;
    };
    /**
     * <p>Enum for Presenter OpenEnterAnimation.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly OpenEnterAnimation: {
        /**
         * None
         * @public
         * @since 4
         *
         */
        None: string;
        /**
         * SlideFromLeft
         * @public
         * @since 4
         *
         */
        SlideFromLeft: string;
        /**
         * SlideFromRight
         * @public
         * @since 4
         *
         */
        SlideFromRight: string;
        /**
         * SlideFromTop
         * @public
         * @since 4
         *
         */
        SlideFromTop: string;
        /**
         * SlideFromBottom
         * @public
         * @since 4
         *
         */
        SlideFromBottom: string;
        /**
         * ZoomIn
         * @public
         * @since 4
         *
         */
        ZoomIn: string;
        /**
         * FadeIn
         * @public
         * @since 4
         *
         */
        FadeIn: string;
    };
    /**
     * <p>Enum for Presenter OpenEnterAnimation.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly LaunchMode: {
        Default: string;
        Single: string;
        SingleTop: string;
        SingleInstance: string;
    };
    private static readonly isPresenterClz = true;
}
export = Presenter;
