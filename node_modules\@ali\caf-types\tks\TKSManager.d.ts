/// <reference types="node" />
import { TKSKey } from './TKSKey';
import TKSOption = require('./TKSOption');
import TKSResult = require('./TKSResult');
/**
 * @friend
 * @draft
 */
declare class TKSManager {
    private static readonly FLAG_HMAC;
    private static readonly HASH_MASK;
    static readonly NONE = 0;
    static readonly MD5 = 3;
    static readonly SHA1 = 4;
    static readonly HmacWithSHA1: number;
    private static iface;
    getKey(name: string, crypto?: string, userPass?: Buffer): TKSResult;
    addKey(name: string, key: TKSKey, crypto?: string, userPass?: Buffer, option?: TKSOption): number;
    modKey(name: string, key: TKSKey, crypto?: string, userPass?: Buffer, option?: TKSOption): number;
    delKey(name: string, crypto?: string, userPass?: Buffer): number;
    newKey(name: string, alg?: number, size?: number, crypto?: string, userPass?: Buffer, option?: TKSOption): TKSResult;
    encrypt(name: string, data: Buffer): TKSResult;
    decrypt(name: string, data: Buffer): TKSResult;
    sign(name: string, hash: number, data: Buffer): TKSResult;
    verify(name: string, hash: number, data: Buffer, sig: Buffer): number;
    statKey(name: string): TKSResult;
    saveData(name: string, data: Buffer, userPass?: Buffer, option?: TKSOption): number;
    loadData(name: string, userPass?: Buffer): TKSResult;
    delData(name: string, userPass?: Buffer): number;
    private createSendMessage;
    private sendMessage;
    private static getInterface;
    private writeKeyWithOption;
}
export = TKSManager;
