import DataResolver = require("yunos/provider/DataResolver");
import DataError = require("yunos/database/sqlite/DataError");
import Cursor = require("yunos/provider/Cursor");
import TypedValues = require("yunos/provider/TypedValues");
import YObject = require("yunos/core/YObject");
/**
 * Private calss.
 * @private
 */
declare class NameValueTable extends YObject {
    public constructor();
    private static putStringSync(resolver: DataResolver, uri: string, name: string, value: string): string;
    private static putString(resolver: DataResolver, uri: string, name: string, value: string, callback: (error: Error, insertedUri?: string) => void): void;
    private static getStringSync(resolver: DataResolver, uri: string, name: string): string;
    private static getString(resolver: DataResolver, uri: string, name: string, callback: (error: DataError, cursor: Cursor) => void): void;
    private static _packageCallMethodArgs(value?: string): TypedValues;
    private static callPutMethod(resolver: DataResolver, uri: string, method: string, name: string, value: string, onComplete: (err: DataError, values?: string) => void): void;
    private static callPutMethodSync(resolver: DataResolver, uri: string, method: string, name: string, value: string): string;
    private static callGetMethod(resolver: DataResolver, uri: string, method: string, name: string, onComplete: (error: DataError, values: TypedValues) => void): void;
    private static callGetMethodSync(resolver: DataResolver, uri: string, method: string, name: string): TypedValues;
    private static readonly NAME = "name";
    private static readonly VALUE = "value";
    private static readonly PROJECTIONS: string[];
    private static readonly NAME_EQ_PLACEHOLDER = "name=?";
}
/**
 * Private calss.
 * @private
 */
declare class NvCache extends YObject {
    private mCachedVersion;
    private mVersionProperty;
    private mUri;
    private mCallGetCommand;
    private mCallPutCommand;
    public constructor(versionProperty: string, uri: string, getCommand: string, putCommand: string);
    private putStringSync(resolver: DataResolver, name: string, value: string): string;
    private putString(resolver: DataResolver, name: string, value: string, callback: (err: DataError, values?: string) => void): void;
    private _checkCache(): void;
    private getStringSync(resolver: DataResolver, name: string): string;
    private getString(resolver: DataResolver, name: string, callback: (error: Error, value?: string) => void): any;
}
/**
 * The interface to access setting items stored in SettingsProvider.
 * @memberof yunos.content
 * @extends yunos.core.YObject
 * @public
 * @since 2
 */
declare class Settings extends NameValueTable {
    public constructor();
    /**
      * <p> The pagelink is used to launch system settings main page.</p>
      * @constant {string}
      * @public
      * @since 2
      */
    public static readonly ACTION_SETTINGS = "page://settings.yunos.com/settings";
    /**
      * <p> The pagelink is used to launch Wi-Fi settings page.</p>
      * @constant {string}
      * @public
      * @since 2
      */
    public static readonly ACTION_WIFI_SETTINGS = "page://settings.yunos.com/wifipage";
    /**
     * <p> The pagelink is used to launch bluetooth settings page.</p>
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_BLUETOOTH_SETTINGS = "page://settings.yunos.com/bluetoothsetting";
    private static readonly ACTION_HOTSPOT = "page://settings.yunos.com/tethering_hotspot_settings";
    /**
     * The pagelink is used to launch VPN settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_VPN_SETTINGS = "page://vpn.yunos.com/main";
    /**
     * The pagelink is used to launch privacy settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_PRIVACY_SETTINGS = "page://settings.yunos.com/privacysettings";
    /**
     * The pagelink is used to launch date and time settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_DATE_TIME_SETTINGS = "page://settings.yunos.com/datetime";
    /**
     * The pagelink is used to launch sound and volume settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_SOUND_SETTINGS = "page://settings.yunos.com/sound";
    /**
     * The pagelink is used to launch display settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_DISPLAY_SETTINGS = "page://settings.yunos.com/display";
    /**
     * The pagelink is used to launch language settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_LANGUAGE_SETTINGS = "page://settings.yunos.com/languagesetting";
    /**
     * The pagelink is used to launch input methods settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_INPUT_METHOD_SETTINGS = "page://settings.yunos.com/languageandinput";
    /**
     * The pagelink is used to launch the page displays device information.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_ABOUT_PHONE_SETTINGS = "page://settings.yunos.com/aboutphone";
    /**
     * The pagelink is used to launch do-not-disturb settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_DONT_DISTURB_MODE_SETTINGS = "page://settings.yunos.com/disturbsettings";
    /**
     * The pagelink is used to launch applications management page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_MANAGE_APPLICATIONS_SETTINGS = "page://settings.yunos.com/applicationmanagementlist";
    /**
     * The pagelink is used to launch application information page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_APPLICATION_DETAILS_SETTINGS = "page://settings.yunos.com/singleappdetailinfo";
    /**
     * The pagelink is used to launch application settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_APPLICATION_SETTINGS = "page://settings.yunos.com/appsmanagement";
    /**
     * The pagelink is used to launch storage settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_STORAGE_SETTINGS = "page://settings.yunos.com/storage";
    /**
     * The pagelink is used to launch network operator selection page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_NETWORK_OPERATOR_SETTINGS = "page://settings.yunos.com/operators";
    /**
     * The pagelink is used to launch data roaming settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_DATA_ROAMING_SETTINGS = "page://settings.yunos.com/mobilenetwork";
    /**
     * The pagelink is used to launch current location settings page.
     * sources.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_LOCATION_SOURCE_SETTINGS = "page://settings.yunos.com/location_service";
    /**
     * The pagelink is used to launch APNs settings page.
     * @constant {string}
     * @public
     * @since 2
     */
    public static readonly ACTION_APN_SETTINGS = "page://settings.yunos.com/mobilenetwork";
    /**
     * Setting not found error.
     * @constant {Error}
     * @public
     * @since 3
     *
     */
    public static readonly SETTING_NOT_FOUND_ERROR: Error;
}
declare namespace Settings {
    /**
     * A group of APIs for accessing normal system settings.
     * @extends yunos.core.YObject
     * @memberof yunos.content.Settings
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    class System extends YObject {
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a string.
         * @example
         * example of getString():
         * var Settings = require("yunos/content/Settings");
         * var dataProvider = require("yunos/provider");
         * var resolver = dataProvider.DataResolver;
         * Settings.System.getString(resolver, Settings.System.LONG_PRESS_TIMEOUT, function(error, value) {
         *     if (!error) {
         *         log.D("example", "getStringCb callback long_press_timeout =" + value);
         *     } else {
         *         log.E("example", "error = " + error);
         *     }
         * });
         *
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {yunos.content.Settings.System~getStringCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getString(resolver: DataResolver, name: string, callback: (error: DataError, value: string) => void): void;
        /**
         * Called after Settings.System.getString() stuff.
         * @callback yunos.content.Settings.System~getStringCb
         * @param {Error} err - successfully, it will be null or undefined.
         * @param {string} value - the corresponding value.
         * @public
         * @since 2
         */
        /**
         * Sync interface to look up a setting item in SettingsProvider by name, retrieving value as a string.
         * Note: Sync API is not recommended as it may block main thread.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {string} def - default value.
         * @return {string} The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        public static getStringSync(resolver: DataResolver, name: string, def: string): string;
        /**
         * <p>Store a setting item into SettingsProvider.</p>
         * @example
         * var Settings = require("yunos/content/Settings");
         * var dataProvider = require("yunos/provider");
         * var resolver = dataProvider.DataResolver;
         * Settings.System.putString(resolver, Settings.System.LONG_PRESS_TIMEOUT, "100000" , function(error, uri) {
         *     if (!error) {
         *         log.D("example", "success, uri = " + uri);
         *     } else {
         *         log.E("example", "failed, error = " + error);
         *     }
         * });
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver  - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {string} value - setting value to store.
         * @param {yunos.content.Settings.System~putStringCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static putString(resolver: DataResolver, name: string, value: string, callback: (error: DataError, value: string) => void): void;
        /**
         * Called after Settings.System.putString() stuff.
         * @callback yunos.content.Settings.System~putStringCb
         * @param {Error} err - successfully, it will be null or undefined.
         * @param {string} uri - the URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        /**
         * <p>Sync interface to store a setting item into SettingsProvider.</p>
         * Note: Sync API is not recommended as it may block main thread.
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver  - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {string} value - setting value to store.
         * @return {string} The URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        public static putStringSync(resolver: DataResolver, name: string, value: string): string;
        /**
         * <p>Store a setting item into SettingsProvider.</p>
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {number} value - setting value to store.
         * @param {yunos.content.Settings.System~putNumberCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static putNumber(resolver: DataResolver, name: string, value: number, callback: (error: DataError, value: string) => void): void;
        /**
         * Called after Settings.System.putNumber() stuff.
         * @callback yunos.content.Settings.System~putNumberCb
         * @param {Error} err - successfully, it will be null or undefined.
         * @param {string} uri - the URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        /**
         * <p>Sync interface to store a setting item into SettingsProvider.</p>
         * Note: Sync API is not recommended as it may block main thread.
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {number} value - setting value to store.
         * @return {Object} The URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        public static putNumberSync(resolver: DataResolver, name: string, value: number): string;
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a number.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {yunos.content.Settings.System~getNumberCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getNumber(resolver: DataResolver, name: string, callback: (error: Error, value?: string | number) => void): void;
        /**
         * Called after Settings.System.getNumber() stuff.
         * @callback yunos.content.Settings.System~getNumberCb
         * @param {Error} err - successfully, it will be null or undefined.
         * @param {number} value - the corresponding value.
         * @public
         * @since 2
         */
        /**
         * Sync interface to look up a setting item in SettingsProvider by name, retrieving value as a number.
         * Note: Sync API is not recommended as it may block main thread.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {number} def - default value.
         * @return {number} The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        public static getNumberSync(resolver: DataResolver, name: string, def: number): number;
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a number.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {number} def - default value.
         * @param {yunos.content.Settings.System~getNumberWithDefaultCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getNumberWithDefault(resolver: DataResolver, name: string, def: number, callback: (value: number) => void): void;
        /**
         * Called after Settings.System.getNumberWithDefault() stuff.
         * @callback yunos.content.Settings.System~getNumberWithDefaultCb
         * @param {number} value - The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a string.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {string} def - default value.
         * @param {yunos.content.Settings.System~getStringWithDefaultCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getStringWithDefault(resolver: DataResolver, name: string, def: string, callback: (value: string) => void): void;
        /**
         * Called after Settings.System.getStringWithDefault() stuff.
         * @callback yunos.content.Settings.System~getStringWithDefaultCb
         * @param {string} value - The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        /**
         * Get URI of setting item by name.
         * @param  {string} name - setting name.
         * @return  {string} The URI of corresponding setting item, or null if something is wrong.
         * @public
         * @since 2
         */
        public static getUriFor(name: string): string;
        private static getHostUriFor(name: string): string;
        private static getContainerUriFor(name: string): string;
        private static readonly VERSION_PROPERTY = "sys.settings_system_version";
        private static readonly LONG_PRESS_TIMEOUT = "long_press_timeout";
        private static readonly HOST_CONTENT_URI = "page://settingsprovider.yunos.com/settingsprovider?table=system";
        private static readonly CONTAINER_CONTENT_URI = "content://settings/system";
        /**
         * <p> The URL for accessing system setting items.</p>
         * @constant {string}
         * @public
         * @example Settings.System.CONTENT_URI
         * @since 2
         */
        public static readonly CONTENT_URI = "page://settingsprovider.yunos.com/settingsprovider?table=system";
        private static sNvCache: NvCache;
        private static readonly CALL_METHOD_GET = "GET_system";
        private static readonly CALL_METHOD_PUT = "PUT_system";
        /**
         * Times display mode. 12 or 24 hours
         *   12
         *   24
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly TIME_12_24 = "time_12_24";
        /**
         * Automatic brightness mode.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly SCREEN_BRIGHTNESS_MODE = "screen_brightness_mode";
        /**
         * SCREEN_BRIGHTNESS_MODE: manual mode.
         * @constant {number}
         * @public
         * @since 2
         */
        public static readonly SCREEN_BRIGHTNESS_MODE_MANUAL = 0;
        /**
         * SCREEN_BRIGHTNESS_MODE: automatic mode.
         * @constant {number}
         * @public
         * @since 2
         */
        public static readonly SCREEN_BRIGHTNESS_MODE_AUTOMATIC = 1;
        /**
         * The brightness of screen backlight. 0 ~ 255.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly SCREEN_BRIGHTNESS = "screen_brightness";
        /**
         * Indicates whether the hint used to prompt user with one handed mode shortcut usage has been shown
         *    1
         *    0
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly ONE_HANDED_MODE_HINT = "one_handed_mode_hint";
        /**
         * Indicates whether the one handed mode is enabled
         *    1
         *    0
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly ONE_HANDED_MODE_ENABLED = "one_handed_mode_enabled";
        /**
         * Indicates whether one handed mode shortcut is enabled
         *    1
         *    0
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly ONE_HANDED_SHORTCUT = "one_handed_mode_shortcut";
        /**
         * Indicates whether the one handed mode acceleration is enabled
         *    1
         *    0
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly ONE_HANDED_MODE_ACC_ENABLED = "one_handed_mode_acc_enabled";
        private static readonly ACCELEROMETER_ROTATION = "accelerometer_rotation";
        /**
          * <p>Default ringtone setting.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly RINGTONE = "ringtone";
        /**
          * <p>Default ringtone setting for sim card 2.</p>
          * @constant {string}
          * @public
          * @since 2
          * @hiddenOnPlatform auto
          */
        public static readonly RINGTONE_2 = "ringtone_2";
        /**
          * <p>The URI of current default ringtone.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly DEFAULT_RINGTONE_URI: string;
        /**
          * <p>The URI of current default ringtone for sim card 2.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly DEFAULT_RINGTONE_URI_2: string;
        /**
          * <p>Default notification sound setting.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly NOTIFICATION_SOUND = "notification_sound";
        /**
         * The URI of current default notification sound.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly DEFAULT_NOTIFICATION_URI: string;
        /**
          * <p>Default alarm alert setting.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly ALARM_ALERT = "alarm_alert";
        /**
         * The URI of current default alarm alert.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly DEFAULT_ALARM_ALERT_URI: string;
        private static readonly MESSAGE_ALERT = "message_alert";
        private static readonly MESSAGE_ALERT_2 = "message_alert_2";
        private static readonly SYSTEM_MESSAGE_ALERT = "system_message_alert";
        private static readonly SYSTEM_MESSAGE_ALERT_2 = "system_message_alert_2";
        private static readonly DEFAULT_MESSAGE_ALERT_URI: string;
        private static readonly DEFAULT_MESSAGE_ALERT_URI_2: string;
        private static readonly SYSTEM_MESSAGE_ALERT_URI: string;
        private static readonly SYSTEM_RINGTONE = "system_ringtone";
        private static readonly SYSTEM_RINGTONE_2 = "system_ringtone_2";
        private static readonly NOTIFICATION_SOUND_2 = "notification_sound_2";
        private static readonly SYSTEM_ALARM_ALERT = "system_alarm_alert";
        private static readonly DEFAULT_ALARM = "default_alarm";
        private static readonly SYSTEM_NOTIFICATION_SOUND = "system_notification_sound";
        private static readonly SYSTEM_NOTIFICATION_SOUND_2 = "system_notification_sound_2";
        private static readonly AUTO_TIME_GPS = "auto_time_gps";
        /**
         * Date format setting.
         *   yyyy-MM-dd
         *   dd-MM-yyyy
         *   MM-dd-yyyy
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly DATE_FORMAT = "date_format";
        /**
         * The time before the device goes to sleep after a period of no user activity.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly SCREEN_OFF_TIMEOUT = "screen_off_timeout";
        private static readonly SETTINGS_ITEM_WEATHER_ANIM = "lockscreen_weather_anim";
        private static readonly POWER_SAVE_MODE_KEY = "pwoerSaveModeKey";
        private static readonly INPUTMETHOD_SECURE_ENABLED = "inputmethod_secure_enabled";
        private static readonly POWER_SAVE_MODE_ON = 1;
        private static readonly POWER_SAVE_MODE_OFF = 0;
        private static readonly POWER_SAVE_MODE_DEFAULT = 0;
        private static readonly SECURE_BAR_GUIDED_KEY = "secure_bar_guided";
        private static readonly SETTINGS_ITEM_WALLPAPER_SWITCH_ENABLE = "lockscreen_wallpaper_switch_enabled";
        private static readonly SETTINGS_ITEM_WALLPAPER_SWITCH_INTERVAL = "lockscreen_wallpaper_switch_interval";
        /**
         * The setting to control if the device vibrates when it is ringing for an incoming call.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly VIBRATE_WHEN_RINGING = "vibrate_when_ringing";
        /**
          * <p>The setting to control if DTMF tones are played while dialing.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly DTMF_TONE_WHEN_DIALING = "dtmf_tone";
        /**
          * <p>The setting to control if the sounds effects are enabled.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly SOUND_EFFECTS_ENABLED = "sound_effects_enabled";
        /**
          * <p>The setting to control if the haptic feedback are enabled.</p>
          * @constant {string}
          * @public
          * @since 2
          */
        public static readonly HAPTIC_FEEDBACK_ENABLED = "haptic_feedback_enabled";
        private static readonly MOTION_DOUBLE_TAP_ENABLE = "motion_double_tap_enable";
        private static readonly MOTION_SWIPE_UP_ENABLE = "motion_swipe_up_enable";
        private static readonly EDGE_ENABLE = "edge_enable";
        private static readonly EDGE_SENSITIVITY_LOW = 0;
        private static readonly EDGE_SENSITIVITY_MEDIUM = 1;
        private static readonly EDGE_SENSITIVITY_HIGH = 2;
        private static readonly EDGE_SENSITIVITY = "edge_sensitivity";
        private static readonly EDGE_SHORT_SQUEEZE_ENABLE = "edge_short_squeeze_enable";
        private static readonly EDGE_SHORT_SQUEEZE_SCREEN_OFF_ENABLE = "edge_short_squeeze_screen_off_enable";
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_LAUNCH_APP = 0;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_TAKE_PHOTO = 1;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_SCREENSHOT = 2;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_TURN_ON_OFF_FLASHLIGHT = 3;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_RECORD_VOICE = 4;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_TURN_ON_OFF_WIFI_HOTSPOT = 5;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_LOCK_UNLOCK_ROTATION = 6;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_ALIPAY_SCAN = 7;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION_ALIPAY_CODE = 8;
        private static readonly EDGE_SHORT_SQUEEZE_ACTION = "edge_short_squeeze_action";
        private static readonly EDGE_SHORT_SQUEEZE_LAUNCH_APP = "Edge_short_squeeze_launch_app";
        private static readonly EDGE_VIBRATION_ENABLE = "edge_vibration_enable";
        private static readonly EDGE_SQUEEZE_OPEN_OOBE = "edge_squeeze_open_oobe";
        private static readonly EDGE_LONG_SQUEEZE_ENABLE = "edge_long_squeeze_enable";
        private static readonly EDGE_VISUAL_FEEDBACK_ENABLE = "edge_visual_feedback_enable";
        private static readonly LOCKSCREEN_SOUNDS_ENABLED = "lockscreen_sounds_enabled";
        private static readonly POWER_ON_MUSIC = "power_on_music";
        private static readonly NAME = "name";
        private static readonly VALUE = "value";
        private static readonly OTA_NEW_VERSION = "ota_new_version";
        private static readonly OTA_AUTO_DOWNLOAD_SW = "ota_auto_download_sw";
    }
    /**************************************************************************************************************************** */
    /**
     * A group of APIs for accessing secure system settings.<br>
     * @extends yunos.core.YObject
     * @public
     * @memberof yunos.content.Settings
     * @since 2
     */
    class Secure extends YObject {
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a string.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {yunos.content.Settings.Secure~getStringCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getString(resolver: DataResolver, name: string, callback: (error: Error, value?: string) => void): void;
        /**
         * Called after Settings.Secure.getString() stuff.
         * @callback yunos.content.Settings.Secure~getStringCb
         * @param {Object} err - successfully, it will be null or undefined.
         * @param {string} value - the corresponding value.
         * @public
         * @since 2
         */
        /**
         * Sync interface to look up a setting item in SettingsProvider by name, retrieving value as a string.
         * Note: Sync API is not recommended as it may block main thread.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {string} def - default value
         * @return {string} The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        public static getStringSync(resolver: DataResolver, name: string, def: string): string;
        /**
         * <p>Store a setting item into SettingsProvider.</p>
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {string} value - setting value to store.
         * @param {yunos.content.Settings.Secure~putStringCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static putString(resolver: DataResolver, name: string, value: string, callback: (error: DataError, value?: string) => void): void;
        /**
         * Called after Settings.Secure.putString() stuff.
         * @callback yunos.content.Settings.Secure~putStringCb
         * @param {Object} err - successfully, it will be null or undefined.
         * @param {string} uri - the URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        /**
         * <p>Sync interface to store a setting item into SettingsProvider.</p>
         * Note: Sync API is not recommended as it may block main thread.
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver  - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {string} value - setting value to store.
         * @return {string} The URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        public static putStringSync(resolver: DataResolver, name: string, value: string): string;
        /**
         * <p>Store a setting item into SettingsProvider.</p>
         * @example
         * example of putNumber():
         * var Settings = require("yunos/content/Settings");
         * var dataProvider = require("yunos/provider");
         * var resolver = dataProvider.DataResolver;
         * Settings.Secure.putNumber(resolver, Settings.Secure.LOCATION_MODE, 1 , function(error, uri) {
         *     if (!error) {
         *         log.D("example", "success, uri = " + uri);
         *     } else {
         *         log.E("example", "failed, error = " + error);
         *     }
         * });
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {number} value - setting value to store.
         * @param {yunos.content.Settings.Secure~putNumberCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static putNumber(resolver: DataResolver, name: string, value: number, callback: (error: DataError, value?: string) => void): void;
        /**
         * Called after Settings.Secure.putNumber() stuff.
         * @callback yunos.content.Settings.Secure~putNumberCb
         * @param {Object} err - successfully, it will be null or undefined
         * @param {string} uri - the URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        /**
         * <p>Sync interface to store a setting item into SettingsProvider.</p>
         * Note: Sync API is not recommended as it may block main thread.
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {number} value - setting value to store.
         * @return {Object} The URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        public static putNumberSync(resolver: DataResolver, name: string, value: number): string;
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a number.
         * @example
         * example of getNumber():
         * var Settings = require("yunos/content/Settings");
         * var dataProvider = require("yunos/provider");
         * var resolver = dataProvider.DataResolver;
         * Settings.Secure.getNumber(resolver, Settings.Secure.LOCATION_MODE, function(error, value) {
         *     if (!error) {
         *         log.D("example", "settingssystemcb callback LOCATION_MODE =" + value);
         *     } else {
         *         log.E("example", "error = " + error);
         *     }
         * });
         *
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {yunos.content.Settings.Secure~getNumberCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getNumber(resolver: DataResolver, name: string, callback: (error: string | Error, value?: number) => void): void;
        /**
         * Called after Settings.Secure.getNumber() stuff.
         * @callback yunos.content.Settings.Secure~getNumberCb
         * @param {Object} err - successfully, it will be null or undefined.
         * @param {number} value - the corresponding value.
         * @public
         * @since 2
         */
        /**
         * Sync interface to look up a setting item in SettingsProvider by name, retrieving value as a number.
         * Note: Sync API is not recommended as it may block main thread.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {number} def - default value.
         * @return {number} The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        public static getNumberSync(resolver: DataResolver, name: string, def: number): number;
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a number.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {number} def - default value.
         * @param {yunos.content.Settings.Secure~getNumberWithDefaultCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getNumberWithDefault(resolver: DataResolver, name: string, def: number, callback: (value: number) => void): void;
        /**
         * Called after Settings.Secure.getNumberWithDefault() stuff.
         * @callback yunos.content.Settings.Secure~getNumberWithDefaultCb
         * @param {number} value - The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a string.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {string} def - default value.
         * @param {yunos.content.Settings.Secure~getStringWithDefaultCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getStringWithDefault(resolver: DataResolver, name: string, def: string, callback: (value: string) => void): void;
        /**
         * Called after Settings.Secure.getStringWithDefault() stuff.
         * @callback yunos.content.Settings.Secure~getStringWithDefaultCb
         * @param {string} value - The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        /**
         * Get URI of setting item by name.
         * @param {string} name - setting name.
         * @return {string} The URI of corresponding setting item, or null if something is wrong.
         * @public
         * @since 2
         */
        public static getUriFor(name: string): string;
        private static getHostUriFor(name: string): string;
        private static getContainerUriFor(name: string): string;
        private static readonly INSTALL_NON_MARKET_APPS = "install_non_market_apps";
        private static readonly LOCKSCREEN_PASSWORD_TYPE = "lockscreen_password_type";
        private static readonly VERSION_PROPERTY = "sys.settings_secure_version";
        private static readonly BATTERY_PERCENTAGE = "battery_percentage";
        private static readonly LOCK_SCREEN_LOCK_AFTER_TIMEOUT = "lock_screen_lock_after_timeout";
        private static readonly INFO_CELLECTION_ENABLE = "info_cellection_enable";
        private static readonly TA_ENABLE = "ta_enable";
        /**
         * <p>Whether location access is enabled. 0=disabled. 1=enabled.</p>
         * @example
         * // turn on location access
         * const Settings = require("yunos/content/Settings");
         * const DataResolver = require("yunos/provider").DataResolver;
         * Settings.Secure.putNumber(DataResolver, Settings.Secure.LOCATION_MODE, Settings.Secure.LOCATION_MODE_ON, function(error) {
         *     if (error) {
         *         log.E("example", "failed to set location mode!");
         *     } else {
         *         log.D("example", "set location mode successfully!");
         *     }
         * });
         *
         * // turn off location access
         * const Settings = require("yunos/content/Settings");
         * const DataResolver = require("yunos/provider").DataResolver;
         * Settings.Secure.putNumber(DataResolver, Settings.Secure.LOCATION_MODE, Settings.Secure.LOCATION_MODE_OFF, function(error) {
         *     if (error) {
         *         log.E("example", "failed to set location mode!");
         *     } else {
         *         log.D("example", "set location mode successfully!");
         *     }
         * });
         *
         * // get status of location access
         * Settings.Secure.getNumber(DataResolver, Settings.Secure.LOCATION_MODE, function(error, mode) {
         *     if (error) {
         *         log.E("example", "failed to get location mode!");
         *     } else {
         *         log.D("example", "get location mode successfully!, mode:" + mode);
         *     }
         * });
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly LOCATION_MODE = "location_mode";
        /**
         * <p>Location access is disabled.</p>
         * @constant {number}
         * @public
         * @since 2
         */
        public static readonly LOCATION_MODE_OFF = 0;
        /**
         * <p>Location access is enabled.</p>
         * @constant {number}
         * @public
         * @since 2
         */
        public static readonly LOCATION_MODE_ON = 3;
        private static readonly LOCATION_PROVIDERS_ALLOWED = "location_providers_allowed";
        private static readonly LOCATION_RTD_ALLOWED = "location_rtd_allowed";
        private static readonly LOCATION_RTD_ALLOWED_OFF = 0;
        private static readonly LOCATION_RTD_ALLOWED_ON = 1;
        private static readonly USER_SETUP_COMPLETE = "user_setup_complete";
        private static readonly AUTO_ANSWER_CALLS = "auto_answer_calls";
        private static readonly HOST_CONTENT_URI = "page://settingsprovider.yunos.com/settingsprovider?table=secure";
        private static readonly CONTAINER_CONTENT_URI = "content://settings/secure";
        /**
         * <p> The URL for accessing secure system setting items.</p>
         * @example Settings.static readonly CONTENT_URI
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly CONTENT_URI = "page://settingsprovider.yunos.com/settingsprovider?table=secure";
        private static sNvCache: NvCache;
        private static readonly CALL_METHOD_GET = "GET_secure";
        private static readonly CALL_METHOD_PUT = "PUT_secure";
        private static readonly NAME = "name";
        private static readonly VALUE = "value";
        private static readonly SMART_AIR_CONDITION = "smart_air_condition";
        private static readonly STEERING_WHEEL_KEY = "steering_wheel_key";
        private static readonly DRIVER_WINDOW_OPERATING = "driver_window_operating";
        private static readonly DRIVER_WINDOW_OPERATING_DIGIT = "driver_window_operating_digit";
        private static readonly LANE_DEPARTURE_WARNING_SYSTEM = "lane_departure_warning_system";
        private static readonly PARKING_AID_SYSTEM = "parking_aid_system";
        private static readonly SPEED_ALERT_SYSTEM = "speed_alert_system";
        private static readonly PEDESTRIAN_ALERT_SYSTEM = "pedestrian_alert_system";
        private static readonly AUTONOMOUS_EMERGENCY_BRAKING = "autonomous_emergency_braking";
        private static readonly REARVIEW_MIRROR_DISPLAY = "rearview_mirror_display";
        private static readonly FULLVIEW_VISION_PARKING = "fullview_vision_parking";
        private static readonly STEERING_OPERATION = "steering_operation";
        private static readonly ACTIVATE_ECO = "activate_eco";
        private static readonly AIR_CONDITIONER = "air_conditioner";
        private static readonly GATE_LID = "gate_lid";
        private static readonly VEHICLE_AIR_PURIFIER_SYSTEM = "vehicle_air_purifier_system";
        private static readonly TRANSMISSION_SYSTEM = "transmission_system";
        private static readonly AUTO_MAP_SCALE = "auto_map_scale";
        /**
         *  Car plate number.
         *  String
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_PLATE_NUMBER = "car_plate_number";
        /**
         *  Car VIN number.
         *  String
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_VIN_NUMBER = "car_vin_number";
        /**
         *  Car engine number.
         *  String
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_ENGINE_NUMBER = "car_engine_number";
        /**
         *  Car exterior color.
         *  "key":"ExteriorColor"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_EXTERIOR_COLOR = "car_exterior_color";
        /**
         *  Car exterior color in digit. To store calibration digit value.
         *  "key":"ExteriorColor"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_EXTERIOR_COLOR_DIGIT = "car_exterior_color_digit";
        /**
         *  Car series name.
         *  "key":"CarSeries"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_SERIES = "car_series";
        /**
         *  Car series name in digit. To store calibration digit value.
         *  "key":"CarSeries"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_SERIES_DIGIT = "car_series_digit";
        /**
         *  Car model name.
         *  "key":"CarModel"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_MODEL = "car_model";
        /**
         *  Car model name in digit. To store calibration digit value
         *  "key":"CarModel"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_MODEL_DIGIT = "car_model_digit";
        /**
         *  Car engine.
         *  Store the car engine type data. e.g. xxx-V8, xxx-V6, no UI show this.
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_ENGINE = "car_engine";
        /**
         *  Car issued year.
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CAR_ISSUED_YEAR = "car_issued_year";
        private static readonly CAR_SUNROOF = "car_sunroof";
        private static readonly CAR_SUNROOF_DIGIT = "car_sunroof_digit";
        /**
         *  <p>Car drive type. PETROL/DESEL/EV/PHEV/LM/REEV</p>
         *
         * @constant {string}
         * @public
         * @since 4
         *
         *
         */
        public static readonly CAR_DYNAMICS_TYPE = "car_dynamics_type";
        /**
         *  <p>Car type.</p>
         *
         * @constant {string}
         * @public
         * @since 4
         *
         *
         */
        public static readonly CAR_TYPE = "car_type";
        private static readonly ONLINE_FM_RADIO = "online_fm_radio";
        private static readonly ONLINE_MUSIC = "online_music";
        private static readonly TRAFFIC_RESTRICTION_REMINDER = "traffic_restriction_reminder";
        /**
         *  Enable voice tips
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly VOICE_TIPS_ENABLE = "voice_tips_enable";
        /**
         *  Enable default wakeup words
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly DEFAULT_WAKEUP_WORDS_ENABLE = "default_wakeup_words_enable";
        /**
         *  Enable custom wakeup words
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CUSTOM_WAKEUP_WORDS_ENABLE = "custom_wakeup_words_enable";
        private static readonly VOICE_TIPS = "voice_tips";
        private static readonly VOICE_PACKAGE_AUTO_UPDATE = "voice_package_auto_update";
        private static readonly VOICE_CALL_REMINDER = "voice_call_reminder";
        /**
         *  Voice speed
         *  voice spped setup, standard, slow, fast, etc
         *  "0" : "slow"
         *  "1" : "standard"
         *  "2" : "fast"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly VOICE_SPEED = "voice_speed";
        /**
         *  Voice type
         *  voice type setup, male, female, children, lin zhiling, etc,
         *  "0" : "male"
         *  "1" : "female"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly VOICE_TYPE = "voice_type";
        /**
         *  system voice type
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly SYSTEM_VOICE_TYPE = "system_voice_type";
        /**
         *  user voice type exist activation
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly USER_VOICE_TYPE_EXIST_ACTIVATION = "user_voice_type_exist_activation";
        /**
         *  user voice type enable
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly USER_VOICE_TYPE_ENABLE = "user_voice_type_enable";
        /**
         *  user voice type
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly USER_VOICE_TYPE = "user_voice_type";
        /**
         *  download user voice type
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly DOWNLOAD_USER_VOICE_TYPE = "download_user_voice_type";
        /**
         *  prefix words enable
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly PREFIX_WORDS_ENABLE = "prefix_words_enable";
        /**
         *  full duplex status
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly FULL_DUPLEX_STATUS = "full_duplex_status";
        /**
         *  incomming call
         *
         * @constant {string}
         * @public
         * @since 5
         *
         */
        public static readonly INCOMMING_CALL = "incomming_call";
        /**
         *  cruise mute
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly CRUISE_MUTE = "cruise_mute";
        /**
         *  light navigation mute
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly LIGHTNAVIGATION_MUTE = "lightnavigation_mute";
        /**
         *  study app network control.  0 off, 1 on
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly NETWORK_DATA_ENABLE_STUDY = "network_data_enable_study";
        /**
         *  network enable control.  0 off, 1 on
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly NETWORK_DATA_ENABLE_ALL = "network_data_enable_all";
        /**
         *  scenario motion control.  0 off, 1 on
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly CONTEXT_MOTION_ENABLE = "context_motion_enable";
        /**
         *  navigation mute
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly NAVIGATION_MUTE = "navigation_mute";
        /**
         *  faceid login
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly FACEID_LOGIN = "faceid_login";
        /**
         *  faceid start engine
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly FACEID_START_ENGINE = "faceid_start_engine";
        /**
         *  initiative voice
         *
         * @constant {number}
         * @public
         * @since 5
         *
         */
        public static readonly INITIATIVE_VOICE = "initiative_voice";
        /**
         *  Quick words enabled
         *  the status of quick words for waking up
         *  0 : disabled
         *  1 : enabled
         *
         * @constant {number}
         * @public
         * @since 4
         *
         */
        public static readonly QUICK_WORDS_ENABLE = "quick_words_enable";
        /**
         *  quality of audio soure
         *  sound quality
         *  "0" : "normal,<= 192kpbs"
         *  "1" : "HQ, 320kpbs"
         *  "2" : "SQ, Nondestructive, >= 1411kpbs"
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly AUDIO_SOURCE_QUALITY = "audio_source_quality";
        /**
         *  network switch for online fmradio.
         *  "0" : off"
         *  "1" : on
         *  default is on
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly NETWORK_DATA_ENABLE_FMRADIO = "network_data_enable_fmradio";
        /**
         *  network switch for music.
         *  "0" : off"
         *  "1" : on
         *  default is on
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly NETWORK_DATA_ENABLE_MUSIC = "network_data_enable_music";
        /**
         *  network switch for online abook.
         *  "0" : off"
         *  "1" : on
         *  default is on
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly NETWORK_DATA_ENABLE_ABOOK = "network_data_enable_abook";
        /**
         *  network switch for online video.
         *  "0" : off"
         *  "1" : on
         *  default is on
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly NETWORK_DATA_ENABLE_VIDEO = "network_data_enable_video";
        /**
         *  voice zone control
         *  "0" : off"
         *  "1" : on
         *  default is on
         *
         * @constant {number}
         * @public
         * @since 6
         * @draft
         *
         */
        public static readonly VOICE_ZONE_CONTROL = "voice_zone_control";
        /**
         *  switch for language detection
         *  "0" : off"
         *  "1" : on
         *  default is on
         *
         * @constant {number}
         * @public
         * @draft
         * @since 6
         *
         */
        public static readonly DIALECT_DETECT_ENABLE = "dialect_detect_enable";
        /**
         * Passenger dulplex: 0 - default(closed)  2 - (60s)
         * @constant {number}
         * @public
         * @draft
         * @since 6
         */
        public static readonly PASSENGER_FULL_DUPLEX_STATUS = "passenger_full_duplex_status";
        /**
         * Square control button customization: 0 - default(null)  1 - (360AI Parking)  2 -(Carlog)
         *           3 - (Switch multimedia volume)  4 - (Carlog photos)  5 - (Carlog photography)
         * @constant {number}
         * @public
         * @draft
         * @since 6
         */
        public static readonly SQUARE_OK_KEY_LONG_PRESS = "square_ok_key_long_press";
        /**
         * touchbar screen saver option
         * @constant {string}
         * @public
         * @draft
         * @since 6
         */
        public static readonly TOUCHBAR_SCREENSAVER_OPTION = "touchbar_screensaver_option";
        /**
          * children lock switch: on or off
          * @constant {string}
          * @public
          * @draft
          * @since 6
          */
        public static readonly CHILDREN_LOCK_SWITCH = "children_lock_switch";
        /**
       * children lock position: left、right、all
       * @constant {string}
       * @public
       * @draft
       * @since 6
       */
        public static readonly CHILDREN_LOCK_POSITION = "children_lock_position";
        /**
        * dialect detect type: 0: Mandarin(default),1: Cantonese,2: Sichuan dialect,3: Shanghai Dialect
        * @constant {number}
        * @public
        * @draft
        * @since 6
        */
        public static readonly DIALECT_DETECT_TYPE = "dialect_detect_type";
        /**
        *
        * @constant {string} type: "1" (on)  "2" (off)
        * @public
        * @draft
        * @since 6
        */
        public static readonly PILOTBUTTON = "pilotbutton";
    }
    /**************************************************************************************************************************** */
    /**
     * A group of APIs for accessing global system settings.<br>
     * @extends yunos.core.YObject
     * @memberof yunos.content.Settings
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    class Global extends YObject {
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a string.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {yunos.content.Settings.Global~getStringCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getString(resolver: DataResolver, name: string, callback: (error: Error, value?: string) => void): void;
        /**
         * Called after Settings.Global.getString() stuff.
         * @callback yunos.content.Settings.Global~getStringCb
         * @param {Error} err - successfully, it will be null or undefined.
         * @param {string} value - the corresponding value.
         * @public
         * @since 2
         */
        /**
         * Sync interface to look up a setting item in SettingsProvider by name, retrieving value as a string.
         * Note: Sync API is not recommended as it may block main thread.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {string} def - default value
         * @return {string} The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        public static getStringSync(resolver: DataResolver, name: string, def: string): string;
        /**
         * <p>Store a setting item into SettingsProvider.</p>
         * @example
         * var Settings = require("yunos/content/Settings");
         * var dataProvider = require("yunos/provider");
         * var resolver = dataProvider.DataResolver;
         * Settings.Global.putString(resolver, Settings.Global.LONG_PRESS_TIMEOUT, "100000" , function(error, uri) {
         *     if (!error) {
         *         log.D("example", "success, uri = " + uri);
         *     } else {
         *         log.E("example", "failed, error = " + error);
         *     }
         * });
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {string} value - setting value to store.
         * @param {yunos.content.Settings.Global~putStringCb} callback - callback function to handle the result
         * @public
         * @since 2
         */
        public static putString(resolver: DataResolver, name: string, value: string, callback: (err: DataError, values?: string) => void): void;
        /**
         * Called after Settings.Global.putString() stuff.
         * @callback yunos.content.Settings.Global~putStringCb
         * @param {Error} err - successfully, it will be null or undefined.
         * @param {string} uri - the URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        /**
         * <p>Sync interface to store a setting item into SettingsProvider.</p>
         * Note: Sync API is not recommended as it may block main thread.
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver  - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {string} value - setting value to store.
         * @return {string} The URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        public static putStringSync(resolver: DataResolver, name: string, value: string): string;
        /**
         * <p>Store a setting item into SettingsProvider.</p>
         * @example
         * example of putNumber():
         * var Settings = require("yunos/content/Settings");
         * var dataProvider = require("yunos/provider");
         * var resolver = dataProvider.DataResolver;
         * Settings.Global.putNumber(resolver,
         *     Settings.Global.AIRPLANE_MODE_ON, enabling ? 1 : 0, function(error, uri) {
         *     if (!error) {
         *         log.D("example", "success, uri = " + uri);
         *     } else {
         *         log.E("example", "failed, error = " + error);
         *     }
         *});
         *
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {number} value - setting value to store.
         * @param {yunos.content.Settings.Global~putNumberCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static putNumber(resolver: DataResolver, name: string, value: number, callback: (err: DataError, values?: string) => void): void;
        /**
         * Called after Settings.Global.putNumber() stuff.
         * @callback yunos.content.Settings.Global~putNumberCb
         * @param {Error} err - successfully, it will be null or undefined
         * @param {string} uri - the URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        /**
         * <p>Sync interface to store a setting item into SettingsProvider.</p>
         * Note: Sync API is not recommended as it may block main thread.
         * @permission WRITE_SETTINGS.permission.yunos.com
         * @permission WRITE_SECURE_SETTINGS.permission.yunos.com
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to store.
         * @param {number} value - setting value to store.
         * @return {string} The URI of corresponding setting item, or undefined if something is wrong.
         * @public
         * @since 2
         */
        public static putNumberSync(resolver: DataResolver, name: string, value: number): string;
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a number.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {yunos.content.Settings.Global~getNumberCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getNumber(resolver: DataResolver, name: string, callback: (error: Error, value?: number) => void): void;
        /**
         * Called after Settings.Global.getNumber() stuff.
         * @callback yunos.content.Settings.Global~getNumberCb
         * @param {Error} err - successfully, it will be null or undefined.
         * @param {number} value - the corresponding value.
         * @public
         * @since 2
         */
        /**
         * Sync interface to look up a setting item in SettingsProvider by name, retrieving value as a number.
         * Note: Sync API is not recommended as it may block main thread.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {number} def - default value.
         * @return {number} The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        public static getNumberSync(resolver: DataResolver, name: string, def: number): number;
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a number.
         * @example
         * example of getNumberWithDefault():
         * var Settings = require("yunos/content/Settings");
         * var dataProvider = require("yunos/provider");
         * var resolver = dataProvider.DataResolver;
         * Settings.Global.getNumberWithDefault(resolver,
         *    Settings.Global.AIRPLANE_MODE_ON, 0, function(value) {
         *        log.D("example", "Settings.Global.getNumberWithDefault airplane_mode_on = " + value);
         *    }
         * );
         *
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {number} def - default value.
         * @param {yunos.content.Settings.Global~getNumberWithDefaultCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getNumberWithDefault(resolver: DataResolver, name: string, def: number, callback: (value: number) => void): void;
        /**
         * Called after Settings.Global.getNumberWithDefault() stuff.
         * @callback yunos.content.Settings.Global~getNumberWithDefaultCb
         * @param {number} value - The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        /**
         * Look up a setting item in SettingsProvider by name, retrieving value as a string.
         * @param {yunos.provider.DataResolver} resolver - resolver to access SettingsProvider with.
         * @param {string} name - setting name to look up in SettingsProvider.
         * @param {string} def - default value.
         * @param {yunos.content.Settings.Global~getStringWithDefaultCb} callback - callback function to handle the result.
         * @public
         * @since 2
         */
        public static getStringWithDefault(resolver: DataResolver, name: string, def: string, callback: (value: string) => void): void;
        /**
         * Called after Settings.Global.getStringWithDefault() stuff.
         * @callback yunos.content.Settings.Global~getStringWithDefaultCb
         * @param {string} value - The setting item's value, or 'def' if it is not found.
         * @public
         * @since 2
         */
        /**
         * Get URI of setting item by name.
         * @param {string} name - setting name.
         * @return {string} The URI of corresponding setting item, or null if something is wrong.
         * @public
         * @since 2
         */
        public static getUriFor(name: string): string;
        private static getHostUriFor(name: string): string;
        private static getContainerUriFor(name: string): string;
        private static readonly VERSION_PROPERTY = "sys.settings_global_version";
        private static readonly MOBILE_DATA = "mobile_data_enabled";
        private static readonly MULTI_SIM_VOICE_CALL_SUBSCRIPTION = "multi_sim_voice_call";
        private static readonly MULTI_SIM_DATA_CALL_SUBSCRIPTION = "multi_sim_data_call";
        private static readonly MULTI_SIM_SMS_SUBSCRIPTION = "multi_sim_sms";
        /**
         * The setting to control if data roaming is enabled for single sim card phone.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly DATA_ROAMING = "data_roaming";
        /**
         * The setting to control if data roaming is enabled for sim card 1.
         * For single sim card phone.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly DATA_ROAMING0 = "data_roaming0";
        /**
         * The setting to control data roaming is enabled for sim card 2.
         * For single sim card phone.
         * @constant {string}
         * @public
         * @since 2
         * @hiddenOnPlatform auto
         */
        public static readonly DATA_ROAMING1 = "data_roaming1";
        /**
         * The setting to control if Airplane Mode is on.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly AIRPLANE_MODE_ON = "airplane_mode_on";
        /**
         * The setting to control if the device fetch the time zone from
         * the network (NITZ) automatically.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly AUTO_TIME_ZONE = "auto_time_zone";
        /**
         * The setting to control if the device fetch the date, time and
         * time zone from the network (NITZ) automatically.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly AUTO_TIME = "auto_time";
        private static readonly TIME_ZONE = "time_zone";
        private static readonly WIFI_NETWORKS_AVAILABLE_NOTIFICATION_ON = "wifi_networks_available_notification_on";
        /**
         * Ringer mode.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly MODE_RINGER = "mode_ringer";
        private static readonly SERVICE_PRIMARY_CARD = "service_primary_card";
        private static readonly PREFERRED_NETWORK_MODE = "preferred_network_mode_type";
        /**
         * The setting to control if the Wi-Fi is on.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly WIFI_ON = "wifi_on";
        private static readonly TOGGOLE_TRAFFIC = "toggole_traffic";
        private static readonly ADB_ENABLED = "adb_enabled";
        private static readonly WIFI_SAVED_STATE = "wifi_saved_state";
        /**
         * The setting to control if the device has been provisioned.
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly DEVICE_PROVISIONED = "device_provisioned";
        private static readonly STAY_ON_WHILE_PLUGGED_IN = "stay_on_while_plugged_in";
        private static readonly SIM_STANDBY = "sim_standby";
        private static readonly DYNAMIC_COLOR_ON = "dynamic_color_on";
        private static readonly NO_DISTURB_MODE = "no_disturb_mode";
        private static readonly NO_DISTURB_MODE_REPEATED_CALL_ON = "no_disturb_mode_repeated_call_on";
        private static readonly DISTURB_MODE = "disturb_mode";
        private static readonly VIP_LIST_KEY = "vip_list_key";
        private static readonly KEY_TIME_SETTING_START_HOUR = "disturb_settings_time_start_hour";
        private static readonly KEY_TIME_SETTING_START_MIN = "disturb_settings_time_start_minute";
        private static readonly KEY_TIME_SETTING_END_HOUR = "disturb_settings_time_end_hour";
        private static readonly KEY_TIME_SETTING_END_MIN = "disturb_settings_time_end_minute";
        private static readonly KEY_TIME_SETTING_TIMER_SWITCH = "disturb_settings_timer_switch";
        private static readonly NO_DISTURB_MODE_MANUAL_ALARM_SET = "com.yunos.no_disturb_mode_manual_alarm_set";
        private static readonly KEY_MANUAL_ALARM_EXIT_HOUR = "disturb_manual_alrm_exit_hour";
        private static readonly KEY_MANUAL_ALARM_EXIT_MIN = "disturb_manual_alrm_exit_minute";
        private static readonly DEFAULT_INSTALL_LOCATION = "default_install_location";
        /**
         * Whether flip the screen can mute or stop the alarm.
         *
         * <p>1 = enable the feature flip the screen can mute or stop the alarm
         * <p>0 = disable the feature
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly FLIP_TO_MUTE = "flip_to_mute";
        private static readonly HOST_CONTENT_URI = "page://settingsprovider.yunos.com/settingsprovider?table=global";
        private static readonly CONTAINER_CONTENT_URI = "content://settings/global";
        /**
         * <p> The URL for accessing global system setting items.</p>
         * @example Settings.static readonly CONTENT_URI
         * @constant {string}
         * @public
         * @since 2
         */
        public static readonly CONTENT_URI = "page://settingsprovider.yunos.com/settingsprovider?table=global";
        private static sNvCache: NvCache;
        private static readonly CALL_METHOD_GET = "GET_global";
        private static readonly CALL_METHOD_PUT = "PUT_global";
        private static readonly NAME = "name";
        private static readonly VALUE = "value";
        /**
         *  custom wakeup words
         *
         * @constant {string}
         * @public
         * @since 4
         *
         */
        public static readonly CUSTOM_WAKEUP_WORDS = "custom_wakeup_words";
    }
}
export = Settings;
