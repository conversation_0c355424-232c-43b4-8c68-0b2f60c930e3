
import PageStyle = require("./PageStyle");
type PageStyleClass = typeof PageStyle;

declare class ISupplement {
    getString: (key: string, value: string) => string;
    getInteger: (key: string, value: number) => number;
}
declare class PLink {
    uri: string;
    eventName: string;
    data: string;
    inGroup: boolean;
    action: string;
    needActive: boolean;
    broadcastToContainer: boolean;
    referer: PLink;
    traceInfo: string;
    transition: Object;
    activateGroup: boolean;
    style: PageStyle;
    targetStackId: number;
    targetDisplayName: string;
    uid: number;
    cloudSyncPolicy: number;

    getPermission(): string[];
    setPermission(value: string[]): void;
    setParam(key: string, value: string): Object;
    getParam(key: string): Object;
    createReply(data: Object): PLink;
    putAttachment(key: string, data: Buffer): void;
    hasAttachment(key: string): boolean;
    removeAttachment(key: string): boolean;
    getAttachment(key: string): Buffer;
    toJson(): string;
    fromJson(str: string): void;
    getEventName(): string;
    getData(): string;
    getSupplement(): ISupplement;
    putSupplement(value: ISupplement): void;

    static PageStyle: PageStyleClass;
    static create(uri: string): PLink;
}
export = PLink;