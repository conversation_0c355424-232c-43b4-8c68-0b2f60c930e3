import YObject = require("../core/YObject");
import BluetoothClass = require("./BluetoothClass");
import BTProfile = require("./BluetoothProfile");
/**
 * <p>An instance of BluetoothDevice represents a remote Bluetooth device.</p>
 * <p>BluetoothDevice instances are returned from {@link yunos.bluetooth.BluetoothAdapter#getPairedDevices} and
 * deviceFound event.</p>
 *
 * @permission BLUETOOTH.permission.yunos.com
 *
 * @extends yunos.core.YObject
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @public
 * @since 2
 */
declare class BluetoothDevice extends YObject {
    private _btApi;
    private _address;
    private _name;
    private _alias;
    private _isPaired;
    private _pairingState;
    private _connectionBits;
    private _bluetoothClass;
    private _rssi;
    private _mode;
    private _uuids;
    private _profiles;
    private _connectionStates;
    public constructor(msg: Object, btApi: Object);
    /**
     * <p>The uuids of the remote device, used to identify the profiles the remote device supports.</p>
     * @name yunos.bluetooth.BluetoothDevice#uuids
     * @type {string[]}
     * @readonly
     * @public
     * @since 3
     */
    public readonly uuids: string[];
    /** @private */
    private readonly rssi: number;
    /**
     * <p>The mac address of the remote bluetooth device.</p>
     * @name yunos.bluetooth.BluetoothDevice#address
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly address: string;
    /**
     * <p>The human-readable name of the remote bluetooth device.</p>
     * @name yunos.bluetooth.BluetoothDevice#name
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly name: string;
    /**
     * <p>The mode of the remote bluetooth device.</p>
     * @name yunos.bluetooth.BluetoothDevice#mode
     * @type {yunos.bluetooth.BluetoothDevice.DeviceMode}
     * @readonly
     * @public
     * @since 4
     */
    public readonly mode: number;
    /**
     * <p>The Bluetooth alias of the remote device.</p>
     * <p>Alias is the locally modified name of a remote device. the length should < 248 bytes.</p>
     * @name yunos.bluetooth.BluetoothDevice#alias
     * @type {string}
     * @throws {Error} If invalid args or fail to set alias.
     * @public
     * @since 2
     */
    public alias: string;
    /**
     * <p>the alias of the remote device, if alias is empty, return the device name.</p>
     * @name yunos.bluetooth.BluetoothDevice#aliasName
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly aliasName: string;
    /**
     * <p>The bluetooth class of this device.</p>
     * @name yunos.bluetooth.BluetoothDevice#bluetoothClass
     * @type {yunos.bluetooth.BluetoothClass}
     * @readonly
     * @public
     * @since 2
     */
    public readonly bluetoothClass: BluetoothClass;
    /**
     * <p>True if the remote device is paired, false otherwise.</p>
     * @name yunos.bluetooth.BluetoothDevice#paired
     * @type {boolean}
     * @readonly
     * @public
     * @since 2
     */
    public readonly paired: boolean;
    /**
     * <p>The current pairing state of the remote device.</p>
     * @name yunos.bluetooth.BluetoothDevice#pairingState
     * @type {yunos.bluetooth.BluetoothDevice.PairingState}
     * @readonly
     * @public
     * @since 2
     */
    public readonly pairingState: string;
    /**
     * <p>All supported profile of this device.</p>
     * @name yunos.bluetooth.BluetoothDevice#profiles
     * @type {BluetoothProfile[]}
     * @readonly
     * @public
     * @since 2
     */
    public readonly profiles: Object[];
    /**
     * <p>The connection state of each supported profile of this device.</p>
     * @name yunos.bluetooth.BluetoothDevice#connectionStates
     * @type {Object.<BluetoothProfile.Name, BluetoothDevice.ConnectionState>}
     * @readonly
     * @public
     * @since 2
     */
    public readonly connectionStates: {
        [key: string]: string; /**BluetoothDevice.ConnectionState*/
    };
    /**
     * <p>Human-readable description.</p>
     * @override
     * @public
     * @since 2
     */
    public toString(): string;
    private _getConnectionStates(connectionBits: number): {
        [key: string]: string;
    };
    private setConnectionState(profileName: string, connectionState: string): void;
    private _getProfilesFromUuids(): BTProfile[];
    private _getProfilesFromBluetoothClass(): BTProfile[];
    private setPermission(type: number, value: number): void;
    private getPermission(type: number): number;
    private connect(): boolean;
    private disconnect(): boolean;
    private static readonly PermissionType: {
        PhoneBook: int;
        Message: int;
        SIM: int;
    };
    private static readonly PermissionValue: {
        Unknown: int;
        Permit: int;
        Forbid: int;
    };
    private static readonly PairingResponse: {
        PairingSuccess: string;
        PairingRemoved: string;
        AuthFailed: string;
        AuthRejected: string;
        AuthCancelled: string;
        AuthTimeout: string;
        RemoteDeviceDown: string;
        RemoteAuthCancelled: string;
        ScanningInProgress: string;
        RepeatedAttempts: string;
        Unknown: string;
    };
    /**
     * <p>The Device Mode.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly Mode: {
        /**
         * BREDR mode device
         * @public
         * @since 4
         */
        Bredr: int;
        /**
         * LE mode device
         * @public
         * @since 4
         */
        Le: int;
        /**
         * BREDR & LE mode both supported device
         * @public
         * @since 4
         */
        Dual: int;
    };
    /**
     * <p>The pairing states.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly PairingState: {
        /**
         * not paired yet
         * @public
         * @since 2
         */
        None: string;
        /**
        * pairing
        * @public
        * @since 2
        */
        Pairing: string;
        /**
        * paired
        * @public
        * @since 2
        */
        Paired: string;
    };
    /**
     * <p>The connection states.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly ConnectionState: {
        /**
         * Disconnected
         * @public
         * @since 2
         */
        Disconnected: string;
        /**
         * Connecting
         * @public
         * @since 2
         */
        Connecting: string;
        /**
         * Connected
         * @public
         * @since 2
         */
        Connected: string;
        /**
         * Disconnecting
         * @public
         * @since 2
         */
        Disconnecting: string;
    };
}
export = BluetoothDevice;
