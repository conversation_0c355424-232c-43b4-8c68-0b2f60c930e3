export declare interface DomainInfoOptions {
    name: string;
    title?: string;
    version: string;
    icon?: string;
    version_code?: number;
    versionCode?: number;
}

export declare interface RecordInfoOptions {
    time?: number;
    domain?: string;
    name: string;
    title: string;
    version: string;
    icon?: string;
    version_code?: number;
    versionCode?: number;
}

export declare interface PageOptions {
    title: string;
    uri: string;
    main: boolean;
    icon: string;
    version: string;
    version_code?: number;
    versionCode?: number;
    extension: boolean;
    capabilities: PageCapabilities;
}

export declare interface PageInfoExtension {
    res_persist_cache?: boolean;
    disabledMMI?: boolean;
    engine?: string;
}

export declare interface UICapability {
    engine?: string;
    display?: string;
    window_config?: string;
    systemui?: boolean;
}

export declare interface PageCapabilities {
    ui?: UICapability;
}

export declare interface PageInfoOptions {
    name?: string;
    title?: string;
    uri?: string;
    main?: boolean;
    icon?: string;
    version?: string;
    version_code?: number;
    versionCode?: number;
    content_path?: string;
    contentPath?: string;
    extension?: PageInfoExtension;
    type?: number;
    capabilities?: PageCapabilities;
}

export declare interface SubscribePageInfoOptions extends PageInfoOptions {
    time?: number;
}

export declare interface PageCoverOptions {
    title: string;
    uri: string;
    version: string;
    icon: string;
    extension: boolean;
    version_code?: number;
    versionCode?: number;
}

export interface UsageRecordOptions {
    name: string;
    title: string;
    iconPath: string;
    version: string;
    versionCode: number;
    time: number;
    type?: string;
}

export interface AppOptions {
    name: string;
    title: string;
    version: string;
    versionCode: number;
    iconPath: string;
}

export interface UpdatePageEventInfos {
    event: string;
    action: string;
    uri: string;
    domainType: number;
}
