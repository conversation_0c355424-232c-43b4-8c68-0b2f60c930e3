import MTopHelper = require('./MTopHelper');
import TKSInfo = require('./TKSInfo');
import { TKSAESKey, TKSDESKey, TKSECK<PERSON>, TKSRSAKey } from './TKSKey';
import TKSManager = require('./TKSManager');
import TKSOption = require('./TKSOption');
import TKSResult = require('./TKSResult');
declare const _default: {
    Manager: typeof TKSManager;
    Option: typeof TKSOption;
    Info: typeof TKSInfo;
    Result: typeof TKSResult;
    AESKey: typeof TKSAESKey;
    DESKey: typeof TKSDESKey;
    RSAKey: typeof TKSRSAKey;
    ECKey: typeof TKSECKey;
    MTopHelper: typeof MTopHelper;
    MD5: number;
    SHA1: number;
    HmacWithSHA1: number;
    AES: number;
    DES: number;
    RSA: number;
    EC: number;
};
export = _default;
