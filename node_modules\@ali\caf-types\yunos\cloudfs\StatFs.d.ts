import YObject = require("../core/YObject");
import DataManagerClass = require("node_dataManager.node");
declare class StatFs extends YObject {
    private static info(path: string): DataManagerClass.statFs;
    private static readonly Error: {
        Success: int;
        ErrAccess: int;
        ErrInterrupt: int;
        ErrIO: int;
        ErrLoop: int;
        ErrNameTooLong: int;
        ErrNoEntry: int;
        ErrNoMem: int;
        ErrOther: int;
    };
}
export = StatFs;
