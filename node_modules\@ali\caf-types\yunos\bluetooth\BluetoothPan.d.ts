import BluetoothProfile = require("./BluetoothProfile");
/**
 * <p>This class provides the APIs to control the Bluetooth Pan Profile.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.bluetooth.BluetoothProfile
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @private
 */
declare class BluetoothPan extends BluetoothProfile {
    /**
     * @private
     */
    private constructor();
    private activate(): number;
    private deactivate(): number;
    /**
     * <p>True if the local pan server is activated, false otherwise.</p>
     * @name yunos.bluetooth.BluetoothPan#activated
     * @type {boolean}
     * @private
     */
    private readonly activated: boolean;
}
export = BluetoothPan;
