import YObject = require("../core/YObject");
/**
 * <p>Represents a Bluetooth advertisement settings.</p>
 * <p>AdvertiseSettings contain additional information and settings of a LE advertisement.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.YObject
 * @memberof yunos.bluetooth
 * @public
 * @since 5
 *
 */
declare class AdvertiseSettings extends YObject {
    private includeName: boolean;
    private includeAppearance: boolean;
    private includeTxPower: boolean;
    private appearance: number;
    private discoverable: boolean;
    private discoverableTimeout: number;
    private duration: number;
    private advertiseTimeout: number;
    private localName: string;
    /**
     * Create a new AdvertiseSettings for LE registerAdvertisement.
     * @param {bool} inc_name - include name of this advertisement
     * @param {bool} inc_appearance - include appearance of this advertisement
     * @param {bool} inc_tx_power - include tx power of this advertisement
     * @param {bool} discoverable - discoverable flag of this advertisement
     * @param {number} appearance - appearance of this advertisement
     * @param {number} discoverable_timeout - discoverable timeout of this advertisement
     * @param {number} duration - duration of this advertisement
     * @param {number} advertise_timeout - advertise timeout of this advertisement
     * @public
     * @since 5
     *
     */
    public constructor(inc_name: boolean, inc_appearance: boolean, inc_tx_power: boolean, appearance: number, discoverable: boolean, discoverable_timeout: number, duration: number, advertise_timeout: number, name: string);
    /**
     * The duration of this advertisement.
     * @name yunos.bluetooth.AdvertiseSettings#Duration
     * @type {string}
     * @readonly
     * @public
     * @since 5
     *
     */
    public readonly Duration: number;
    /**
     * The advertise timeout of this advertisement.
     * @name yunos.bluetooth.AdvertiseSettings#AdvertiseTimeout
     * @type {number}
     * @readonly
     * @private
     *
     */
    private readonly AdvertiseTimeout: number;
}
export = AdvertiseSettings;
