/*
 * Copyright (C) 2019 Alibaba Group Holding Limited. All Rights Reserved.
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import ContentProviderHolder = require("plugin/provider/lib/ContentProviderHolder");
import DataError = require("yunos/database/sqlite/DataError");
import DataObserver = require("yunos/provider/DataObserver");
import DataResolver = require("yunos/provider/DataResolver");
import ProviderProxyContainer = require("plugin/provider/lib/ProviderProxyContainer");

declare class DataResolverDelegate {
    static getCaller(): Object;
    static getContentProvider(caller: Object, name: string, userId: number, stable: boolean,
        callback: (err: DataError, cph: ContentProviderHolder) => void): ContentProviderHolder;
    static createProviderProxy(name: string, providerHolder: ContentProviderHolder, resolver: DataResolver): ProviderProxyContainer;
    static registerObserver(observer: DataObserver, callback: (err: DataError) => void): boolean;
    static unregisterObserver(observer: DataObserver, callback: (err: DataError) => void): boolean;
    static notifyChange(uri: string, callback: (err: DataError) => void): boolean;
}

export = DataResolverDelegate;