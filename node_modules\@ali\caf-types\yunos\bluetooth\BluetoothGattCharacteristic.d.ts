/// <reference types="node" />
import BluetoothGattDescriptor = require("./BluetoothGattDescriptor");
import YObject = require("../core/YObject");
/**
 * <p>Represents a Bluetooth GATT Characteristic.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.YObject
 * @memberof yunos.bluetooth
 * @public
 * @since 3
 *
 */
declare class BluetoothGattCharacteristic extends YObject {
    private _id;
    private _descriptors;
    private _value;
    /**
     * Create a new BluetoothGattCharacter object for BLE.
     * @param {string} uuid - the uuid of the characteristic
     * @param {number} instanceId - the handle of the characteristic
     * @public
     * @since 5
     * @draft
     *
     */
    public constructor(uuid: string, instanceId?: number);
    /**
     * A list of descriptors for this characteristic.
     * @name yunos.bluetooth.BluetoothGattCharacteristic#descriptors
     * @type {yunos.bluetooth.BluetoothGattDescriptor[]}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly descriptors: BluetoothGattDescriptor[];
    /**
     * Return the descriptor with the given uuid.
     * @param {string} uuid - the uuid of a descriptor.
     * @return {yunos.bluetooth.BluetoothGattDescriptor} the descriptor
     * @public
     * @since 3
     *
     */
    public getDescriptor(uuid: string): BluetoothGattDescriptor;
    /**
     * Add the descriptor to this characteristic.
     * @param {yunos.bluetooth.BluetoothGattDescriptor} descriptor - the descriptor to be added.
     * @return {number} the length of new array
     * @public
     * @since 3
     *
     */
    public addDescriptor(descriptor: Object): void;
    /**
     * The id of gatt Characteristic.
     * @name yunos.bluetooth.BluetoothGattCharacteristic#gattId
     * @type {Object} uuid:string, instanceId:number
     * @readonly
     * @public
     * @since 5
     *
     */
    public readonly gattId: {
        uuid: string;
        instanceId?: number;
    };
    /**
     * The uuid of this characteristic.
     * @name yunos.bluetooth.BluetoothGattCharacteristic#uuid
     * @type {string}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly uuid: string;
    /**
     * The instance id of this characteristic.
     * @name yunos.bluetooth.BluetoothGattCharacteristic#instanceId
     * @type {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly instanceId: number;
    /**
     * The value data of this characteristic.
     * @name yunos.bluetooth.BluetoothGattCharacteristic#value
     * @type {Buffer}
     * @readonly
     * @public
     * @since 3
     *
     */
    /**
     * @private
    */
    public value: Buffer;
    /**
     * <p>Human-readable description.</p>
     * @override
     * @public
     * @since 5
     */
    public toString(): string;
}
export = BluetoothGattCharacteristic;
