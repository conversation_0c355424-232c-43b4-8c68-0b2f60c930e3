/**
 * Error class to represent error information of file system
 * @extends Error
 * @memberof yunos.cloudfs
 * @param {number} code - The error code
 * @param {string} message - The error string
 * @public
 * @since 2
 */
declare class FileSystemError extends Error {
    private _message;
    private _code;
    public constructor(message: string, code?: number);
    private readonly errCode: number;
    /**
     * Enumeration for error messages of file system
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly ErrorMsg: {
        /**
         * Operation not permitted
         * @public
         * @since 2
         */
        EPERM: string;
        /**
         * No such file or directory
         * @public
         * @since 2
         */
        ENOENT: string;
        /**
         * Bad file descriptor
         * @public
         * @since 2
         */
        EBADF: string;
        /**
         * Permission denied
         * @public
         * @since 2
         */
        EACCES: string;
        /**
         * File already exists
         * @public
         * @since 2
         */
        EEXIST: string;
        /**
         * Not a directory
         * @public
         * @since 2
         */
        ENOTDIR: string;
        /**
         * Illegal operation on a directory
         * @public
         * @since 2
         */
        EISDIR: string;
        /**
         * Invalid argument
         * @public
         * @since 2
         */
        EINVAL: string;
        /**
         * File table overflow
         * @public
         * @since 2
         */
        ENFILE: string;
        /**
         * Too many open files
         * @public
         * @since 2
         */
        EMFILE: string;
        /**
         * File too large
         * @public
         * @since 2
         */
        EFBIG: string;
        /**
         * No space left on device
         * @public
         * @since 2
         */
        ENOSPC: string;
        /**
         * Read-only file system
         * @public
         * @since 2
         */
        EROFS: string;
        /**
         * Too many links
         * @public
         * @since 2
         */
        EMLINK: string;
        /**
         * The type of the parameter error
         * @public
         * @since 2
         */
        ParamTypeError: string;
        /**
         * Same files when copy or remove files
         * @public
         * @since 2
         */
        SameFileError: string;
        /**
         * Unknown error message
         * @public
         * @since 2
         */
        UnknownError: string;
    };
    /**
     * Enum for error code of file system
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly ErrorCode: {
        [index: string]: number;
    };
}
export = FileSystemError;
