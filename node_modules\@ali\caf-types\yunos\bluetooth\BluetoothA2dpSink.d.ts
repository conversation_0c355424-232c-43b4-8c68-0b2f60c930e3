import EventEmitter = require("../core/EventEmitter");
/**
* @memberof yunos.bluetooth
* @relyon YUNOS_SYSCAP_BLUETOOTH
* @permission BLUETOOTH.permission.yunos.com
* @private
*/
declare class BluetoothA2dpSink extends EventEmitter {
    private _btApi;
    private name;
    private _address;
    public constructor(address?: string);
    private handleA2dpSinkEvents(): void;
    private handleListenerChangeEvents(): void;
    private Suspend(): boolean;
    private Resume(): boolean;
    private static readonly EventName: {
        [key: string]: string;
    };
}
export = BluetoothA2dpSink;
