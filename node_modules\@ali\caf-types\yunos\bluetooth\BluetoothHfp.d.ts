import BluetoothProfile = require("./BluetoothProfile");
/**
 * <p> This class defines methods that shall be used by application to manage profile connection, calls states and calls actions. </p>
 *
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @permission BLUETOOTH.permission.yunos.com
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @public
 * @since 5
 */
declare class BluetoothHfp extends BluetoothProfile {
    private _address;
    public constructor(address?: string);
    private handleBtEvents(): void;
    /**
     * <p> Establish the sco connection with remote device. </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#audioconnectionchanged
     * @public
     * @since 2
     */
    public connectAudio(): boolean;
    /**
     * <p> Disconnect the sco connection with remote device. </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#audioconnectionchanged
     * @public
     * @since 2
     */
    public disconnectAudio(): boolean;
    /**
     * <p> Check SCO connection state is connected or not. </p>
     * @return {boolean}  true if SCO connected,false if <PERSON><PERSON> disconnected;
     * @public
     * @since 2
     */
    public audioConnected(): boolean;
    /**
     * <p> Starts voice recognition </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#voicerecognitionstate
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public startVoiceRecognition(): boolean;
    /**
     * <p> Stop voice recognition </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#voicerecognitionstate
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public stopVoiceRecognition(): boolean;
    /**
     * <p> control the current ivi mic or speaker gain </p>
     * @param {yunos.bluetooth.BluetoothHfp.VolumeType} type - speaker or mic,new lite project only support mic control;
     * @param {number} volume - the setting volume gain;
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#callmicgain
     * @public
     * @since 2
     */
    public volumeControl(type: number, volume: number): boolean;
    /**
     * <p> Places a call with specified number or at specified memory location. </p>
     * @param {string} number - valid phone number;
     * @param {number} memory - valid memory location;
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public dialNumber(number: string, memory: number): boolean;
    /**
     * <p> Sends DTMF code </p>
     * @param {number} tones - ASCII code,Possible code values : 0,1,2,3,4,5,6,7,8,9,A,B,C,D,*,#
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @public
     * @since 2
     */
    public sendTones(tones: number): boolean;
    /**
     * <p> Accepts a call </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public answerCall(): boolean;
    /**
     * <p>  Rejects a call or hangup a call </p>.
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public hungupCall(): boolean;
    /**
     * <p> get list of all calls in any state. </p>.
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public getCurrentCalls(): boolean;
    /**
     * <p> Get the number corresponding to last voice tag recorded on AG. </p>.
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#lastnum
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public getLastNumber(): boolean;
    /**
     * <p> Get the local phone number info.  </p>.
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#subscriberinfo
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public retrieveSubscriberInfo(): boolean;
    /**
     * <p> Get the sco connection state. </p>.
     * @return {yunos.bluetooth.BluetoothHfp.AudioState} current audio state.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public getAudioState(): int;
    /**
     * <p> Release the current active call and accept the incoming call. </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public releaseAndAcceptCall(): boolean;
    /**
     * <p> swap the call between active and hold call. </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public swapCall(): boolean;
    /**
     * <p> Used in multiparty call, Release all hold calls or sets UDUB for a waiting call(release waiting call). </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public releaseAllCall(): boolean;
    /**
     * <p> Add a call join the party call. </p>
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public joinCall(): boolean;
    /**
     * <p> Request private consultation mode with specified call idx.
     *     Place all calls on hold except the call indicated by idx. </p>
     * @param {number} idx - the specified call idx request private consultation mode.
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#curcall
     * @public
     * @since 2
     */
    public releasePrivateMode(idx: number): boolean;
    /**
     * <p> Release the specified active call only by idx. </p>
     * @param {number} idx - the specified call idx request to release.
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @public
     * @since 2
     */
    public releaseSpecialCall(idx: number): boolean;
    /**
     * <p> Send the special AT command to AG. </p>
     * @param  {string} cmd - the AT command.
     * @return {boolean}  true if command has been issued successfully,false otherwise;
     * @fires yunos.bluetooth.BluetoothHfp#commandcomplete
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public sendCommand(cmd: string): boolean;
    /**
     * <p>Get support information of hfp features.</p>
     * @return {Object} the object containing feature information of the remote hfp device.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public getCapabilities(): Object;
    /**
     * <p>Get hfp information of remote device.</p>
     * @return {Object} the object containing hfp information of the remote device.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public getCurrentEvents(): Object;
    /**
    * <p>Set phone call multi zone.</p>
    * @param {number} zoneId - see yunos.device.AudioManager#ZoneId
    * @param {string} sessionName - session name which get from request audio session
    * @return {boolean}  true if command has been issued successfully,false otherwise
    * @public
    * @since 6
    */
    public setCallMultiZone(zoneId: number, sessionName: string): boolean;
    private static readonly AtCommandType: {
        NONE: int;
        BRSF: int;
        BAC: int;
        CIND: int;
        CIND_STATUS: int;
        CMER: int;
        CHLD: int;
        CMEE: int;
        BIA: int;
        CLIP: int;
        CCWA: int;
        COPS: int;
        CLCC: int;
        BVRA_1: int;
        BVRA_0: int;
        VGS: int;
        VGM: int;
        ATD: int;
        BLDN: int;
        ATA: int;
        CHUP: int;
        BTRH: int;
        VTS: int;
        BCC: int;
        BCS: int;
        CNUM: int;
        NREC: int;
        BINP: int;
        VENDOR: int;
    };
    /**
     * <p> Control Volume type</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly VolumeType: {
        /**
         * Speaker
         * @public
         * @since 5
         */
        Speaker: int;
        /**
         * MIC
         * @public
         * @since 5
         */
        Microphone: int;
    };
    /**
     * <p> The SCO connecton state </p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly AudioState: {
        /**
         * unknown
         * @public
         * @since 5
         */
        Unknown: int;
        /**
         * Disconnected
         * @public
         * @since 5
         */
        Disconnected: int;
        /**
         * Connecting
         * @public
         * @since 5
         */
        Connecting: int;
        /**
         * Connected
         * @public
         * @since 5
         */
        Connected: int;
        /**
         * Disconnecting
         * @public
         * @since 5
         */
        Disconnecting: int;
    };
    /**
     * <p> The remote device sco connection state </p>
     * @enum {string}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly ConnectionInfo: {
        /**
         * the remote device address.
         * @public
         * @since 5
         */
        Address: string;
        /**
         * the previous state.
         * @public
         * @since 5
         */
        PreviousState: string;
        /**
         * the current state.
         * @public
         * @since 5
         */
        CurrentState: string;
    };
    /**
     * <p> The current call information </p>
     * @enum {string}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly CurrentCallInfo: {
        /**
         * the current call index.
         * @public
         * @since 5
         */
        Index: string;
        /**
         * the current call direction.
         * @public
         * @since 5
         */
        Incoming: string;
        /**
         * the current call status.
         * @public
         * @since 5
         */
        Status: string;
        /**
         * the current call whether or not in party.
         * @public
         * @since 5
         */
        InParty: string;
        /**
         * the current call whether or not number.
         * @public
         * @since 5
         */
        NumberPresent: string;
        /**
         * the current call number.
         * @public
         * @since 5
         */
        Number: string;
    };
    /**
     * <p> Events of bluetooth hfp profile </p>
     * @enum {string}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly EventName: {
        /**
         * <p>the hfp features information fetched</p>
         * @private
         */
        FeaturesFetched: string;
        /**
         * <p>incoming call ringtone updated</p>
         * @private
         */
        IncomingCallRingtoneUpdated: string;
        /**
         * <p>audio state changed</p>
         * @public
         * @since 5
         */
        AudioConnectionChanged: string;
        /**
         * <p>voice recognotion state changed</p>
         * @private
         */
        VoiceRecognition: string;
        /**
         * <p>get current call event</p>
         * @public
         * @since 5
         */
        CurrentCall: string;
        /**
         * <p>get last call number event</p>
         * @private
         */
        LastNumber: string;
        /**
         * <p>get AG subscriber info event</p>
         * @private
         */
        SubscriberInfo: string;
        /**
         * <p>call swap event</p>
         * @private
         */
        CallSwapped: string;
        /**
         * <p>incoming call waiting</p>
         * @private
         */
        CallWaiting: string;
        /**
         * <p>incoming call number event</p>
         * @private
         */
        CallIncomingNumber: string;
        /**
         * <p>call on hold event</p>
         * @private
         */
        CallOnHold: string;
        /**
         * <p>call on unhold event</p>
         * @private
         */
        CallUnhold: string;
        /**
         * <p>call setup incoming event</p>
         * @private
         */
        CallSetupIncoming: string;
        /**
         * <p>call is dialing event</p>
         * @private
         */
        CallSetupDialing: string;
        /**
         * <p>call is alerting event</p>
         * @private
         */
        CallSetupAlerting: string;
        /**
         * <p>call started event</p>
         * @private
         */
        CallStart: string;
        /**
         * <p>call is end event</p>
         * @private
         */
        CallEnd: string;
        /**
         * <p>call on idle event</p>
         * @private
         */
        CallIdle: string;
        /**
         * <p>remote device speaker gain</p>
         * @private
         */
        SpeakerGain: string;
        /**
         * <p>remote device mic gain</p>
         * @public
         * @since 5
         */
        MicGain: string;
        /**
         * <p>a call is ringing event</p>
         * @private
         */
        RingIndicator: string;
        /**
         * <p>AT command complete event</p>
         * @private
         */
        CommandComplete: string;
    };
}
export = BluetoothHfp;
