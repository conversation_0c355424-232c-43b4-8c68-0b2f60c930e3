import EventEmitter = require("../core/EventEmitter");
import Page = require("../page/Page");
/**
 *
 * <p>Class for accessing and modifying key-value data.
 * Use <code>ConfigStore.getInstance()</code> to return
 * a ConfigStore through which you can retrieve and modify its
 * values by using <code>get</code> and <code>put</code> functions.
 * Only one instance is returned to any callers for the same filename,
 * meaning they will see each other's edits as soon as they were made.
 * When <code>apply</code> or <code>applySync</code> is invoked
 * the memory data will be written to file system.</p>
 *
 * <p>All your changes are in memory until you
 * call <code>apply</code> or <code>applySync</code>.
 * If the application was killed by system and ConfigStore will not be notified, the memory data may lost.</p>
 *
 * <p><em>Note: This class is not recommended when using across multiple processes.</em>
 * Please use [DataProvider]{@link yunos.provider.DataProvider}.</p>
 * <p>For more information about using ConfigStore, read the guide of Data Storage.</p>
 *
 * @example
 *
 * var cs = ConfigStore.getInstance();
 *
 * cs.put("first", false);
 * var isFirst = cs.get("first");
 *
 * // rewrite to file
 * cs.apply();
 *
 *
 * @extends yunos.core.EventEmitter
 * @memberof yunos.content
 * @public
 * @since 2
 */
declare class ConfigStore extends EventEmitter {
    private _file;
    private _applyWriting;
    private applyQueue;
    private map;
    private static instanceMap;
    /**
     * <p>Retrieve instance of ConfigStore by file name without file extension.
     * It will use the default value if you do not provide a file name.
     * Use this static method to get ConfigStore instance and do not use <code>new ConfigStore()</code>.</p>
     * @example
     * // You can get config value in your application by using getInstance().
     * var cs = ConfigStore.getInstance();
     *
     * // or use a specific file name.
     * var cs = ConfigStore.getInstance("settings");
     *
     * @param {string} [fileName] - File's name, the name of file you want to save.
     * @return {yunos.content.ConfigStore} Instance of ConfigStore.
     * @throws {TypeError} If the type of fileName is not a string.
     *
     * @public
     * @since 2
     */
    public static getInstance(context?: Page | null | string, fileName?: string): ConfigStore;
    /**
     * <p><code>new ConfigStore()</code> is not recommended,
     * use [ConfigStore.getInstance()]{@link yunos.content.ConfigStore.getInstance} instead.<p>
     * @private
     */
    private constructor(context: Page, fileName: string);
    private init(context: Page, fileName: string): void;
    private checkAlive(): void;
    /**
     * <p>Retrieve a value from the config store.</p>
     *
     * @example
     *
     * ConfigStore.getInstance().get("myKey", "defaultValue");
     *
     * @param {string} key - The key of the value to retrieve.
     * @param {Object} [defaultValue] - Default value. Value to return if this value does not exist.
     * @return {Object} Returns the value if it exists, or default value.
     * @throws {TypeError} If the type of key is not a string.
     *
     * @public
     * @since 2
     */
    public get(key: string, defaultValue: Object): Object;
    /**
     * <p>Put a value in the memory data, to be written
     * back once {@link applySync} or {@link apply} is called.</p>
     * @example
     *
     * ConfigStore.getInstance().put("myKey", "myValue");
     *
     * @param {string} key - The key string to modify.
     * @param {Object} value - The new value for the input key. The value must be able to be serialized.
     * @return {yunos.content.ConfigStore} Returns a reference to this, so you can chain put calls together.
     * @throws {TypeError} If the type of key is not a string or the type of value is a function.
     *
     * @public
     * @since 2
     */
    public put(key: string, value: Object): this;
    /**
     * <p>Remove a value from the memory data, to be written
     * back once {@link applySync} or {@link apply} is called.</p>
     * @example
     *
     * ConfigStore.getInstance().remove("myKey");
     *
     * @param {string} key - The key string to remove.
     * @return {yunos.content.ConfigStore} Returns a reference to this, so you can chain put calls together.
     * @throws {TypeError} If the type of key is not a string.
     *
     * @public
     * @since 2
     */
    public remove(key: string): this;
    /**
     * <p>This function is used for the {@link apply} method.</p>
     * @callback yunos.content.ConfigStore~applyCallback
     * @param {?Error} [err] - The error object which contains details when the invocation is failed.<br>
     * The object is null when the invocation is succeeded.
     * @public
     * @since 2
     */
    /**
     * <p>Apply the memory data to file system asynchronously.</p>
     *
     * <p>Asynchronous apply data to disk and you won't be notified of
     * any failures unless provide a callback which will be invoked by system.</p>
     * @param {yunos.content.ConfigStore~applyCallback} [callback] - The callback will be invoked when IO completed.
     * @example
     *
     * ConfigStore.getInstance().apply((err) => {
     *     if (err !== null) {}
     * });
     *
     * // or ignore result
     * ConfigStore.getInstance().apply();
     *
     * @public
     * @since 2
     */
    public apply(callback?: (err: Error) => void): void;
    private writeFile(): void;
    /**
     * <p>Apply the memory data to file system synchronously. It may block main thread.</p>
     * <p>Consider using {@link apply} if you don't care about the return value.</p>
     *
     * @return {boolean} Returns true if the data were successfully written
     * to file system.
     *
     * @example
     *
     * var res = ConfigStore.getInstance().applySync();
     * if (res) {}
     *
     * @public
     * @since 2
     */
    public applySync(): boolean;
    /**
     * <p>Get the number of items in this ConfigStore.</p>
     *
     * @name yunos.content.ConfigStore#count
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly count: number;
    /**
     * <p>Release the memory data. Call this only when you want to trim memory.</p>
     *
     * <p>If this function was invoked, you can not call {@link put} or {@link get} again
     * unless you get a new instance by [ConfigStore.getInstance()]{@link yunos.content.ConfigStore.getInstance}.</p>
     *
     * @example
     *
     * ConfigStore.getInstance().destroy();
     *
     * @public
     * @since 2
     */
    public destroy(): void;
}
export = ConfigStore;
