import EventEmitter = require("../core/EventEmitter");
/**
* @memberof yunos.bluetooth
* @relyon YUNOS_SYSCAP_BLUETOOTH
* @permission BLUETOOTH.permission.yunos.com
* @private
*/
declare class BluetoothAvrcpTarget extends EventEmitter {
    private _btApi;
    private name;
    private _address;
    public constructor(address?: string);
    private handleAvrcpTargetEvents(): void;
    private handleListenerChangeEvents(): void;
    private static readonly PlayControlCmd: {
        Play: int;
        Pause: int;
        Stop: int;
        Next: int;
        Previous: int;
        PressFastForward: int;
        ReleaseFastForward: int;
        PressRewind: int;
        ReleaseRewind: int;
    };
    private static readonly EventName: {
        [key: string]: string;
    };
}
export = BluetoothAvrcpTarget;
