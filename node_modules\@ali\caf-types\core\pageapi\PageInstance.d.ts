import PageWindow = require("./PageWindow");
import PageLink = require("./PageLink");
import {PageInfoOptions} from "./PageImplInterface";
import {ConnectionListener} from "core/pageapi/ServiceProxy";

declare class PageInstance {
    pageObject: Object;
    /**
     * @friend
     * @draft
     */
    mainWindow: PageWindow;
    environment: {
        orientation: number, rotation: number, fontScale: number,
        language: string, country: string,
        layoutDirection?: boolean // FIXME: layoutDirection not exists
    };
    sourceLink: PageLink;
    pageInfo: PageInfoOptions;
    saveState: Function;
    retrieveSavedState: Function;

    sendLink(pageLink: PageLink, callback?: (...args: Object[]) => void): Object;

    sendLinkSync(pageLink: PageLink, timeout: number): Object;
    stopPage: Function;
    hidePage: Function;
    handleBackKeyPressed: Function;
    createPageCover: Function;
    on: Function;
    once: Function;
    removeListener: Function;
    rootPath: string;
    displayName: string;
    uri: string;
    path: string;
    pageId: string;
    pageType: string;
    isHomeShell: boolean;
    updateServiceSwitch: Function;
    getServiceProxy: Function;

    getServiceProxySync(serviceUri: string, connectionListener: ConnectionListener, keepAlive?: boolean, userData?: Object, defaultProxy?: Object): Object;

    registerPageStatusListener: Function;
    updatePageStatus: Function;

    subscribeEvent(eventName: string, callback: (...args: Object[]) => void): Object;

    unsubscribeEvents(handles: (...args: Object[]) => void | Function[]): boolean;

    stopFastRender: Function;
    unregisterPageStatusListener: Function;

    subscribeEvents(eventNames: string[], callback: (...args: Object[]) => void): Object;

    overridePendingTransition: Function;
    getTopPageUri: Function;
    getTopPage: Function;

    removeAllListeners(): void;

    inquirePermissionCallback: Function;

    moveToDisplay(dispName:string, position:{x:number, y:number}, callback?: (err:Object, flag: boolean) => void): void;
}

export = PageInstance;

