import EventEmitter = require("../core/EventEmitter");
/**
 * <p>Model can be bind to you markup, when the property in model changed, the view related to this property will update auto.</p>
 * @example
 *
 * class MyModel extends Model {
 *     initProperties() {
 *         return { batteryPower: this.getCurrentBatteryPower() };
 *     }
 *
 *     getCurrentBatteryPower() {
 *         ...
 *     }
 *
 *     onBatteryChange(value) {
 *        this.batteryPower = value;
 *     }
 *
 * }
 *
 * <CompositeView>
 *     <TextView id="battery_power" text="{{batteryPower}}"/>
 * </CompositeView>
 *
 * @extends yunos.core.EventEmitter
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @public
 * @since 3
 */
declare class Model extends EventEmitter {
    private _properties;
    public constructor();
    /**
     * <p>Init and return the properties you want to bind to view or want to observe</p>
     * @return {Object}
     * @protected
     * @abstract
     * @since 3
     *
     */
    protected initProperties(): {
        [key: string]: Object;
    };
    /**
     * @private
     */
    private readonly properties: {
        [key: string]: Object;
    };
    private _setup(): void;
    /**
     * <p>Add a property to model.</p>
     * @param {string} propertyName - Property name
     * @param {*} [initialValue] - Initial value for the property
     * @public
     * @since 3
     *
     */
    public addProperty(propertyName: string, initialValue?: Object): void;
    /**
     * <p>Adds the listener function to the end of the listeners array for the event named eventName.&nbsp;
     * No checks are made to see if the listener has already been added.&nbsp;
     * Multiple calls passing the same combination of eventName and listener will result in the listener being added,&nbsp;
     * and called, multiple times.</p>
     * @param {string} eventName - The name of the event.
     * @param {Function} listener - The callback function.
     * @public
     * @override
     * @since 4
     *
     */
    public on(eventName: string, listener: (...args: Object[]) => void, /* emitAtOnce is private for now */ emitAtOnce?: boolean): this;
    private static readonly isModelClz = true;
}
export = Model;
