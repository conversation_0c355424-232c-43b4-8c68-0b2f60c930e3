import Router = require("./Router");
import Presenter = require("./Presenter");
import StackPresenter = require("./StackPresenter");
import CompositeView = require("../ui/view/CompositeView");
/**
 * <p>StackRouter providers route with a stack structure.</p>
 * @example
 * let router = new StackRouter();
 * let container = CompositeView.create({
 *     width: window.width,
 *     height: window.height
 * });
 * router.route("foo", () => {
 *     return require("./presenter/FooPresenter");
 * });
 * router.container = container;
 * router.navigate("foo");
 * @extends yunos.appmodel.Router
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @public
 * @since 3
 */
declare class StackRouter extends Router {
    protected _presenter: Presenter;
    protected _activatedPresenter: Presenter;
    protected initializeRouter(): void;
    public container: CompositeView;
    public presenter: StackPresenter;
    public readonly activatedPresenter: Presenter;
    protected getPresenter(path: string, data: Object, newConfig: Object, direction: number): Presenter;
    protected onNavigate(presenter: Presenter, direction: number): number;
    public destroy(): void;
    public empty(): void;
}
export = StackRouter;
