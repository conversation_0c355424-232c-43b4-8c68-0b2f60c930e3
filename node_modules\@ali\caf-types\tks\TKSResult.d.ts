/**
 * @friend
 * @draft
 */
declare class TKSResult {
    /** Operation successed */
    static readonly OKAY = 0;
    /** Operation failed:Internal error */
    static readonly ERR_INTERNAL = -1;
    /** Operation failed: Denied */
    static readonly ERR_DENY = -2;
    /** Operation failed: Key not found */
    static readonly ERR_NOTFOUND = -3;
    /** Operation failed: Key exists already */
    static readonly ERR_EXISTS = -4;
    /** Operation failed: Key expired */
    static readonly ERR_EXPIRED = -5;
    /** Operation failed: Service not ready */
    static readonly ERR_UNREADY = -7;
    /** Operation failed: Arguments is illegal */
    static readonly ERR_ILLARG = -8;
    /** Operation failed: Out of memory */
    static readonly ERR_NOMEMORY = -9;
    /** Operation failed: Key is limited */
    static readonly ERR_KEYLIMIT = -10;
    /** Operation failed: Wrong user password */
    static readonly ERR_WRONGPWD = -11;
    /** Operation failed:Input data too large */
    static readonly ERR_DATABIG = -12;
    private code;
    private data;
    constructor();
    getCode(): number;
    setCode(code: number): this;
    getData(): Object;
    setData(data: Object): this;
    getCodeName(): "OKAY" | "Internal error" | "Denied" | "Key not found" | "Key expired" | "Key exists already" | "Service not ready" | "Arguments is illegal" | "Out of Memory" | "Key number is limit" | "Wrong password" | "Input data is too large" | "Unknown";
    isSuccess(): boolean;
}
export = TKSResult;
