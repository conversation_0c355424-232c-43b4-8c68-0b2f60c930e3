import EventEmitter = require("yunos/core/EventEmitter");
declare class ResourceSubscriber extends EventEmitter {
    private static instance;
    private _nodeCloudData;
    private constructor();
    /**
     * <p>Get instance of ResourceSubscriber by page messages.<br>
     * Use this static method to get ResourceSubscriber instance and do not using new ResourceSubscriber().</p>
     *
     * @public
     */
    static getInstance(procId: string, solution?: number, filePath?: string): ResourceSubscriber;
    private createResponse;
    private init;
    sendReqResourceSignal(): Object;
    sendAppConfirmSignal(fileInfo: string): Object;
    setCheckFileCallback(callback: (value: string) => boolean): Object;
    sendEvent(procId: string, parma: string, data: string, extend: string): Promise<Object>;
    stop(): void;
    destroy(): void;
    private eventListener;
    static readonly EventListenerNotify: {
        RESOURCE_CHANGE_EVENT: string;
    };
    static readonly EventAction: {
        RESOURCE_UPDATE_ENABLE: string;
        RESOURCE_EVENT_DISABLE: string;
        RESOURCE_ROLLBACK_ENABLE: string;
    };
    static readonly SolutionType: {
        GeneralContracting: int;
        HalfContracting: int;
    };
}
declare namespace ResourceSubscriber {
    class Response {
        code: number;
        msg: string;
        data: Object;
    }
}
export = ResourceSubscriber;
