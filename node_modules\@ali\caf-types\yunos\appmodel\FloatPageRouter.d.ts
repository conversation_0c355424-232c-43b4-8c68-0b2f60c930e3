import StackRouter = require("./StackRouter");
import FloatPagePresenter = require("./FloatPagePresenter");
import StackPresenter = require("./StackPresenter");
import { PresenterConfigDes, StackType } from "./core/define";
import FloatPage = require("yunos/page/FloatPage");
/**
 * <p>FloatPageRouter helps you to simplify
 * application development for FloatPage - A page with two present modes</p>
 * @example
 * let router = new FloatPage();
 * router.run(pageInstance);
 * router.container = pageInstance.window;
 * router.route("foo", () => {
 *     return require("./presenter/FooPresenter");
 * }, {
 *     mode: FloatPage.Mode.QuickControl
 * });
 * router.navigate("foo");
 * @extends yunos.appmodel.StackRouter
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @public
 * @since 5
 */
declare class FloatPageRouter extends StackRouter {
    protected _presenter: FloatPagePresenter;
    protected configParser(config: PresenterConfigDes): boolean;
    protected searchPathParser(originalPath: string): string;
    public readonly presenter: StackPresenter;
    protected readonly stack: StackType;
    /**
     * <p>Return the current mode.</p>
     * @name yunos.appmodel.FloatPageRouter#mode
     * @type {number}
     * @public
     * @since 5
     */
    public mode: number;
    public destroy(): void;
    public static DEFAULT_SEARCH_PATH: {
        presenterSearchPath: string;
        viewSearchPath: string;
        modelSearchPath: string;
    };
    protected printHistroy(): void;
    public run(page: FloatPage, routePageUri?: boolean, dispatchPageEvent?: boolean): void;
    public canRoutePath(path: string, config?: {
        [key: string]: Object;
    }): boolean;
}
export = FloatPageRouter;
