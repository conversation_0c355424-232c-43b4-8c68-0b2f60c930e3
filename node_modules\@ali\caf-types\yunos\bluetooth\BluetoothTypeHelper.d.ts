/**
 * <p>structure to hold the device information.</p>
 * @readonly
 * @public
 * @since 5
 */
export interface BTDeviceInfo {
    /**
     * device name
     * @public
     * @since 5
     */
    name: string;
    /**
     * alias name
     * @public
     * @since 5
     */
    alias: string;
    /**
     * device address
     * @public
     * @since 5
     */
    address: string;
    /**
     * device majorDeviceClass
     * @public
     * @since 5
     */
    majorDeviceClass: number;
    /**
     * device minorDeviceClass
     * @public
     * @since 5
     */
    minorDeviceClass: number;
    /**
     * device serviceClass
     * @public
     * @since 5
     */
    serviceClass: number;
    /**
     * uuids
     * @public
     * @since 5
     */
    uuids: string[];
    /**
     * paired flag
     * @public
     * @since 5
     */
    isPaired: boolean;
    /**
     * device bond state
     * BOND_STATE_NONE = 0,
     * BOND_STATE_BONDING = 1,
     * BOND_STATE_BONDED = 2,
     * @public
     * @since 5
     */
    pairingState: string;
    /**
     * connected flag
     * @public
     * @since 5
     */
    connectionBits: number;
    /**
     * received strength signal
     * @public
     * @since 5
     */
    rssi: number;
    /**
     * device mode,bredr,le,dual
     * @public
     * @since 5
     */
    mode: number;
}
