declare enum ErrorCode {
    kOK = 0,
    kNoPrivilege = 1,
    kNotIdentified = 2,
    kSignalNotSuppot = 3,
    kSignalNotSupportSet = 4,
    kSignalNotSupportGet = 5,
    kSignalNotSupportSub = 6,
    kInvalidInputFormat = 7,
    kIpcNoConnection = 8,
    kIpcConnectFailed = 9,
    kIpcRequestFailed = 10,
    kTimeout = 11,
    kExceptionError = 12
}
declare class Vapi {
    private uuid;
    private resultViaCallback;
    static readonly ErrorCode: typeof ErrorCode;
    /**
     * Creates Vapi instance.
     * @param {string} uuid. User's identifier.
     * @memberof Vapi
     */
    constructor(uuid?: string, resultViaCallback?: boolean);
    /**
     *
     * Initialize Vapi
     * @returns {string} json format string: {"RESULT":int, "DESC":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    init(): string;
    /**
     *
     * Shutdown Vapi
     * @param {boolean} [force=true] indicate whether Vapi should be shutdown by force.
     * @returns {void}
     * @memberof Vapi
     */
    shutdown(force?: boolean): void;
    /**
     *
     * Get value with indicated signal name and parameter synchronously.
     * @param {string} signalName signal name.
     * @param {string} param signal parameter.
     * @param {number} [timeoutMsec=3000] timeout value in milliseconds.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    get(signalName: string, param: string, timeoutMsec?: number): string;
    /**
     *
     * Get value with indicated signal name and parameter asynchronously.
     * @param {string} signalName: signal name.
     * @param {string} param: signal parameter.
     * @param {(reuslt: string) => void} callback: callback to receive call result.
     * @param {number} [timeoutMsec=3000]: timeout value in milliseconds.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    getAsync(signalName: string, param: string, callback: (reuslt: string) => void,
        timeoutMsec?: number): string;
    /**
     *
     * Set value with indicated signal name and value synchronously.
     * @param {string} signalName: signal name.
     * @param {string} param: signal parameter.
     * @param {number} [timeoutMsec=3000]: timeout value in milliseconds.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    set(signalName: string, value: string, timeoutMsec?: number): string;
    /**
     *
     * Set value with indicated signal name and value asynchronously.
     * @param {string} signalName: signal name.
     * @param {string} value: signal parameter.
     * @param {(result: string) => void} callback: callback to receive call result.
     * @param {number} [timeoutMsec=3000]: timeout value in milliseconds.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    setAsync(signalName: string, value: string, callback: (result: string) => void, timeoutMsec?: number): string;
    /**
     *
     * Verify signal whether support get set and pub synchronously
     * @param {string} signalName: signal name.
     * @param {number} [timeoutMsec=3000]: timeout value in milliseconds.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    verify(signalName: string, timeoutMsec?: number): string;
    /**
     *
     * Verify signal whether support get set and pub asynchronously.
     * @param {string} signalName: signal name.
     * @param {(result: string) => void} callback: callback to receive call result.
     * @param {number} [timeoutMsec=3000]: timeout value in milliseconds.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    verifyAsync(signalName: string, callback: (result: string) => void, timeoutMsec?: number): string;
    /**
     *
     * Subscribe notification of the indicated signal.
     * @param {string} signalName signal name
     * @param {(result: string) => void} callback: callback to receive call result.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    subscribe(signalName: string, callback: (result: string) => void): string;
    /**
     *
     * unsubscribe notification of the indicated signal.
     * @param {string} signalName: signal name.
     * @returns {string} json format string: {"RESULT":int, "DESC":string, "CMD":string, "PARAM":string}
     * value of "RESULT" follows the definition of Vapi.ErrorCode
     * @memberof Vapi
     */
    unsubscribe(signalName: string): string;
    private static sharedIdentifier;
    private id;
}
export = Vapi;
