/// <reference types="node" />
import EventEmitter = require("../core/EventEmitter");
/**
 * The bluetooth socket used to send/receive data between connected peers.
 * On the client side, to communicate with a service in a remote device, first create a new
 * BluetoothSocket object with the device and uuid, then call `connect()` to start connect to the
 * server, event 'connect' will be sent once connected and event 'error' will be sent when there's
 * error. When the socket is connected, `write()` can be used to send data, and listen to `data`
 * event to receive data.
 *
 * @example
 * const uuid = "uuid-fake";
 * const address = "mac-address-fake";
 * const socket = new BluetoothSocket(address, uuid);
 * socket.on("data", data => {
 *     console.info("received data", data);
 * });
 * socket.on("error", err => {
 *     console.error("error", err);
 * });
 * socket.connect();
 *
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @public
 * @since 2
 * @hiddenOnPlatform auto
 */
declare class BluetoothSocket extends EventEmitter {
    private _address;
    private _connecting;
    private uuid;
    private btApi;
    private fd;
    private _socket;
    /**
     * constructor to create a BluetoothSocket with the service uuid in device with the address.
     * @param  {string} address - the target remote device
     * @param  {string} uuid    - the service uuid of the remote device.
     * @return {yunos.bluetooth.BluetoothSocket}         the new created BluetoothSocket object
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public constructor(address: string, uuid?: string);
    /**
     * <p>the mac address of the remote device.</p>
     * @name yunos.bluetooth.BluetoothSocket#address
     * @type {string}
     * @readonly
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public readonly address: string;
    /**
     * <p>Check the socket whether is connecting.</p>
     * @name yunos.bluetooth.BluetoothSocket#connecting
     * @type {boolean}
     * @readonly
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public readonly connecting: boolean;
    /**
     * start connect to the service in remote device.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public connect(): void;
    private _createSocket(fd: number): void;
    /**
     * <p>Check whether the socket is connected or not.</p>
     * @name yunos.bluetooth.BluetoothSocket#connected
     * @type {boolean}
     * @readonly
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public readonly connected: boolean;
    /**
     * close the connection
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public close(): void;
    /**
     * <p>This callback is called by write method.</p>
     * @callback yunos.bluetooth.BluetoothSocket~writeCallback
     * @param {?Error} [err] - The error object which contains details when the invocation is failed.<br>
     * The object is null when the invocation is succeeded.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    /**
     * send the data on the socket.
     * @param  {Buffer}   data     - the data to send
     * @param  {yunos.bluetooth.BluetoothSocket~writeCallback} callback - executed when the data is finally written out
     * @throws {Error} when socket is not connected yet
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public write(data: Buffer, callback: Function): void;
}
export = BluetoothSocket;
