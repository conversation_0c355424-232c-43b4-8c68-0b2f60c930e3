import YObject = require("../core/YObject");
import App = require("./App");
import PageLink = require("../page/PageLink");
import PageInfo = require("./PageInfo");
import SubscribePageInfo = require("./SubscribePageInfo");
import { DomainInfoOptions, PageOptions, PageInfoOptions, PageCoverOptions, UpdatePageEventInfos } from "core/pageapi/PageImplInterface";
/**
 * <p>Class for retrieving various kinds of information related
 * to the application packages that are currently installed on the device.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.app
 * @public
 * @since 2
 * @hiddenOnPlatform auto
 */
declare class AppManager extends YObject {
    private static instance;
    /**
     * <p>Get instance of AppManager.</p>
     * @return {yunos.app.AppManager} Instance of AppManager.
     * @public
     * @since 2
     */
    public static getInstance(): AppManager;
    /**
     * <p>Release instance of AppManager.</p>
     * @throws {Error} If the instance of AppManager is not exist.
     * @public
     * @since 2
     */
    public static releaseInstance(): void;
    /**
     * <p>Create a instance of AppManager.</p>
     * <p>Do not use new AppManager(), use [AppManager.getInstance()]{@link yunos.app.AppManager} instead.
     * @private
     */
    private constructor();
    /**
     * This method will be called when get all installed application packages from system.
     * @callback yunos.app.AppManager~getAppsCallback
     * @param {?Error} err - Errors of getting apps.
     * @param {yunos.app.App[]} results - Array of all application packages.
     * @public
     * @since 2
     */
    /**
     * <p>Get a array of all application packages that are installed on the device.</p>
     * @permission QUERY_PAGE.permission.yunos.com
     * @param {yunos.app.AppManager~getAppsCallback} callback - Callback of getApps.
     * @public
     * @since 2
     */
    public getApps(callback: (err: Error, results?: App[]) => {}): void;
    /**
     * This method will be called when get all pages .
     * @callback yunos.app.AppManager~getPagesCallback
     * @param {?Error} err - Errors of getting pages.
     * @public
     * @since 2
     */
    /**
     * <p>Get a array of all pages that are installed on the device.</p>
     * @permission QUERY_PAGE.permission.yunos.com
     * @param {yunos.app.AppManager~getPagesCallback} callback - Callback of getPages.
     * @public
     * @since 2
     */
    public getPages(callback: (err: Error, results?: PageOptions[]) => {}, options?: Object): void;
    /**
     * This method will be called when get page info with uri from system.
     * @param {string} uri - page's uri.
     * @callback yunos.app.AppManager~getPageCallback
     * @param {?Error} err - Errors of getting page info.
     * @param result - page info.
     * @public
     * @since 3
     *
     */
    /**
     * <p>Get page info with uri that are installed on the device.</p>
     * @permission QUERY_PAGE.permission.yunos.com
     * @param {string} uri - page's uri.
     * @param {yunos.app.AppManager~getPageCallback} callback - Callback of getPage.
     * @public
     * @since 3
     *
     */
    public getPage(uri: string, callback: (err: Error, result?: PageInfoOptions) => {}): void;
    /**
     * This method will be called when install application packages from ypp files.
     * @callback yunos.app.AppManager~installCallback
     * @param {?Error} err - Errors of installing apps.
     * @public
     * @since 2
     */
    /**
     * <p>Install a app from ypp file(non-system app only install self).</p>
     * @param {string} appPackagePath - Absolute path or page uri of ypp file.
     * @param {yunos.app.AppManager~installCallback} callback - Callback of intalling.
     * @public
     * @since 2
     */
    public install(appPackagePath: string, callback: (err: Error) => {}): void;
    /**
     * This method will be called when uninstall application packages.
     * @callback yunos.app.AppManager~uninstallCallback
     * @param {?Error} err - Errors of uninstalling.
     * @public
     */
    /**
     * <p>Uninstall the given app, removing it completely from the device. <p>
     * @param {string} appName - Name of app which to be uninstalled.
     * @param {yunos.app.AppManager~uninstallCallback} callback - Callback of uninstalling.
     * @public
     */
    public uninstall(appName: string, callback: (err: Error) => {}): void;
    /**
     * <p>Install a overlay from file.</p>
     * @param {string} overlayPath - Absolute path of overlay.
     * @param {yunos.app.AppManager~installCallback} callback - Callback of intalling.
     * @public
     * @since 2
     */
    public installOverlay(overlayPath: string, callback: (err: Error) => {}): void;
    /**
     * This method will be called when uninstall overlay.
     * @callback yunos.app.AppManager~uninstallCallback
     * @param {?Error} err - Errors of uninstalling.
     * @public
     */
    /**
     * <p>Uninstall the given overlay, removing it completely from the device. <p>
     * @param {string} appName - Name of overlay which to be uninstalled.
     * @param {yunos.app.AppManager~uninstallCallback} callback - Callback of uninstalling.
     * @public
     */
    public uninstallOverlay(appName: string, callback: (err: Error) => {}): void;
    /**
     * This method will be called when load subpackage from cloud.
     * @callback yunos.app.AppManager~loadSubPackageCallback
     * @param {?Error} err - Errors of loading subpackage.
     * @public
     * @since 2
     */
    /**
     * <p>Load a subpackage from cloud or return immediately when subpackage has been installed.</p>
     * @param {string} domain - Domain name.
     * @param {string} subPackageName - SubPackage name.
     * @param {yunos.app.AppManager~loadSubPackageCallback} callback - Callback of loading subpackage.
     * @public
     * @since 2
     */
    public loadSubPackage(domain: string, subPackageName: string, callback: (err: Error) => {}): void;
    /**
     * This method will be called when resolve page.
     * @callback yunos.app.AppManager~resolveCallback
     * @param {?Error} err - Errors of resolving.
     * @param {yunos.app.PageInfo[]} results - all resolved pages.
     * @public
     * @since 2
     */
    /**
     * <p>Resolve the given pageLink. <p>
     * @permission QUERY_PAGE.permission.yunos.com
     * @param {yunos.page.PageLink} pageLink - link of page which to be resolved.
     * @param {yunos.app.AppManager~resolveCallback} callback - Callback of resolving.
     * @public
     * @since 2
     */
    public resolvePage(pageLink: PageLink, callback: (err: Error, results?: PageInfo[]) => {}): void;
    /**
     * This method will be called when get the size of app installed's directory.
     * @callback yunos.app.AppManager~getInstalledAppSizeCallback
     * @param {?Error} err - Errors of resolving.
     * @param {number} size - the size(unit KB) of app installed's directory.
     * @public
     * @since 2
     */
    /**
     * <p>get asset directory of installed app size. <p>
     * @param {string} domain - this format is 'page://domain'.
     * @param {yunos.app.AppManager~getInstalledAppSizeCallback} callback - Callback of getInstalledAppSize.
     * @public
     * @since 2
     */
    public getInstalledAppSize(domain: string, callback: (err: Object, size?: number) => {}): void;
    private subscribePage(options: Object, callback: (err: Error) => {}): void;
    private unsubscribePage(uri: string, callback: (err: Error) => {}): void;
    private getSubscribedPages(callback: (err: Error, results?: SubscribePageInfo[]) => {}): void;
    private getPageCover(uri: string, callback: (err: Error, result?: PageCoverOptions) => {}): void;
    private getPageCovers(callback: (err: Error, result?: PageCoverOptions[]) => {}): void;
    private getDomainInfo(uri: string, callback: (err: Error, result?: DomainInfoOptions) => {}): void;
    /**
     * This method will be called when get domain information with uri from system.
     * @callback yunos.app.AppManager~receiveBroadcastCallback
     * @param {?Error} err - Error of receive update page event.
     * @param info - Update page info, including action and uri.
     * @friend
     */
    /**
     * Receive the app manager broadcast event.
     * @param {string} event - The event name to received.
     * @param {yunos.app.AppManager~receiveBroadcastCallback} callback - The function which is called when the event is emitted.
     * @return {Object} The handle to identify the receiver and to unreceive the event.
     * @example
     * const AppManager = require("yunos/app/AppManager");
     * let handle = AppManager.getInstance().receiveBroadcast(AppManager.BroadcastEvents.UpdateApp,
     *     function(err, info) {
     *         if (!err) {
     *             console.log(info.uri + " is " + info.action);
     *             AppManager.getInstance().unreceiveBroadcast(handle);
     *         }
     *     });
     * @friend
     */
    receiveBroadcast(event: string, callback: (err: Error, result?: UpdatePageEventInfos) => {}): Object;
    /**
     * Unreceive the app manager broadcast event.
     * @param {Object} handles - The handle returned by receiveBroadcast.
     * @return {boolean} true if the event is succeeded to unreceived. Otherwise return false.
     * @friend
     */
    unreceiveBroadcast(handle: Object): boolean;
    /**
     * <p>Enum for App Manager Broadcast Events.</p>
     * <p>Constants for receiveBroadcast.</p>
     * @enum {string}
     * @readonly
     * @friend
     */
    static readonly BroadcastEvents: {
        /**
         * Triggered when app is installed/updated/uninstalled or
         * overlay installed/uninstalled or account permission changed.
         * @friend
         */
        UpdateApp: string;
        UpdateOverlay: string;
        AccountPermissionChanged: string;
    };
    /**
     * Enum for DomainType
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly DomainType: {
        All: int;
        Builtin: int;
        ThirdParty: int;
    };
}
export = AppManager;
