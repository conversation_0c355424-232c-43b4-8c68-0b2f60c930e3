import Page = require("yunos/page/Page");
/**
 * <p>Class for cache an application's resources.</p>
 * @private
 */
declare class ResourceCache {
    private map;
    private static instanceMap;
    private page;
    public constructor(page: Page);
    private put(key: string, value: Object): void;
    private static getInstance(domainName?: string, options?: {
        subscheme?: string;
        page?: Page;
    }): ResourceCache;
    private get(key: string): Object;
    private clear(): void;
}
export = ResourceCache;
