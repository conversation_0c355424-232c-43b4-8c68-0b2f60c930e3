import {EventEmitter} from "events";
import {Interface, Message} from "ubus";

declare class ServiceProxy { }

declare namespace ServiceProxy {
    class ServiceProxy {
        constructor(name: string, path: string);
        onConnected: Object;
        onDisconnected: Object;
        closeConnection: (disableCallback?: boolean) => void;
        createMethodCallMessage: (method: string) => Message;
        callMethod: (msg: Object, callback: Function, timeout: number) => void;
        callMethodSync: (msg: Object, timeout: number) => Message;
        subscribeEvent: (name: string, callback: Function) => Object;
        unsubscribeEvent: (handle: Object) => void;
        _iface: Interface;
    }
    class ConnectionListener extends EventEmitter {
        new (): ConnectionListener;
        static ConnectedEvent: string;
        static DisconnectedEvent: string;
        static FailedEvent: string;
    }
}

export = ServiceProxy;