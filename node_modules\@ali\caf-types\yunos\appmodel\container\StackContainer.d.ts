import View = require("../../ui/view/View");
import CompositeView = require("../../ui/view/CompositeView");
declare class StackContainer extends CompositeView {
    private _handleResponsive;
    private _topChild;
    private _bottomChild;
    private handleResponsive: boolean;
    private peekChild(): View;
    private pushChild(item: View): void;
    private afterPushChild(): void;
    private beforePopChild(): void;
    private popChild(): View;
    private popToChild(view: View): View[];
    private popToRootChild(): View[];
}
export = StackContainer;
