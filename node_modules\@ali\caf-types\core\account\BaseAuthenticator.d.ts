import AuthenticatorServiceAdaptor = require("./lib/AuthenticatorServiceAdaptor");
/**
 * BaseAuthenticator class. Account app should implements this class
 * @class BaseAuthenticator
 * @extends Page
 */
declare class BaseAuthenticator extends AuthenticatorServiceAdaptor {
    constructor(authTokenType: string);
    /**
     * @readonly
     * @override
     * @ignore
     */
    readonly type: string;
    /**
     * add
     * @method BaseAuthenticator#add
     * @param {accountType} accountType to be added
     * @param {String} authenticatorName the specified authenticator
     * @param {Object} addAccountOptions
     * @param {String} pageUri after login accountmanager will link to this pageUri
     * @param {BaseAuthenticator#addCallback} callback The callback that handles the response.
     * @public
     */
    add(accountType: string, authTokenType: string, addAccountOptions: object, pageUri: string, appinfo: object, callback: (x: object) => void): void;
    /**
     * @getAuthInfo get the auth info of an account from a account app
     * @method BaseAuthenticator#getAuthInfo
     * @param {Account} account the specified accout
     * @param {String} authenticatorName the specified auth token type
     * @param {Object} options
     * @param {String} pageUri after login accountmanager will link to this pageUri
     * @param {bool} uiMark mark if got login ui if the account has not logined
     * @param {Object} appinfo caller's info contains uid and pid
     * @param {BaseAuthenticator#getAuthInfoCallback} callback The callback that handles the response.
     * @public
     */
    getAuthInfo(account: object, authTokenType: string, options: object, pageUri: string, uiMark: boolean, appinfo: object, callback: (x: object) => void): void;
    /**
     * remove account for account app.
     * @method BaseAuthenticator#remove
     * @param {Account} account to be removed
     * @param {String} authTokentype
     * @param {bool} uiMark
     * @param {Object} appinfo
     * @param {BaseAuthenticator#removeCallback} callback The callback that handles the response.Return true if authenticator allowed.
     * @public
     */
    remove(account: object, authTokenType: string, uiMark: boolean, appinfo: object, callback: (x: boolean) => void): void;
    /**
     * update account for account app.
     * @method BaseAuthenticator#update
     * @param {Account} account to be updated
     * @param {Object} appinfo
     * @param {BaseAuthenticator#updateCallback} callback The callback that handles the response. Return true if authenticator allowed.
     * @public
     */
    update(account: object, appinfo: object, callback: (x: boolean) => void): void;
    /**
     * validatePassword account for account app.
     * @method BaseAuthenticator#validatePassword
     * @param {Account} account account to be validated
     * @param {Object} options the options arguments of validatePassword
     * @param {BaseAuthenticator#validatePasswordCallback} callback The callback that handles the response.
     * @public
     */
    validatePassword(account: object, options: object, callback: (x: object) => void): void;
}
export = BaseAuthenticator;
