import YFileOutputStream = require("./FileOutputStream");
import FileSystemError = require("./FileSystemError");
/**
 * Class for writting regular files
 * @example
 * let YFileWriter = require("yunos/cloudfs/FileWriter");
 * let file = new YFile("/data/write_test/file");
 * let writeTmp = new YFileWriter(file);
 * let write = new YFileWriter(writeTmp.getFd());
 * or use file object directly
 * write = new YFileWriter(new YFile("/data/write_test/file"));
 * let bytesWrite = write.writeSync(buffer, 0, buffer.length);
 * write.closeSync();
 * @extends yunos.cloudfs.FileOutputStream
 * @memberof yunos.cloudfs
 * @public
 * @since 2
 */
declare class FileWriter extends YFileOutputStream {
    /**
     * Creates a new FileWriter object given a file or file descriptor object
     * @param {yunos.cloudfs.File|yunos.cloudfs.FileDescriptor} fileOptObj - The file or fd object
     * @param {yunos.cloudfs.File.OpenMode} mode - The mode for writing
     * @throws {yunos.cloudfs.FileSystemError} If invalid args
     * @public
     * @since 2
     */
    public constructor(fileOptObj: Object, append?: number);
    /**
     * The function which indicates the result of a asynchronous write a string to file
     * @callback yunos.cloudfs.FileWriter~writeFileCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @public
     * @since 2
     */
    /**
     * Asynchronous write a entire string to file
     * This method will cover the current content in the file
     * @param {string} data - The data which will be written to the file
     * @param {string} [encoding = "utf8"] encoding - The string's encoding
     * @param {yunos.cloudfs.FileWriter~writeFileCallback} callback The callback when completed
     * @public
     * @since 2
     */
    public writeFile(data: string, encodingIn: Object, callbackIn: (err: FileSystemError) => void): void;
    /**
     * Synchronous write a entire string to file
     * This method will cover the current content in the file
     * @param {string} data - The data which will be written to the file
     * @param {string} [encoding = "utf8"] encoding - The string's encoding
     * @throws {yunos.cloudfs.FileSystemError} If I/O exception
     * @public
     * @since 2
     */
    public writeFileSync(data: string, encoding: string): void;
}
export = FileWriter;
