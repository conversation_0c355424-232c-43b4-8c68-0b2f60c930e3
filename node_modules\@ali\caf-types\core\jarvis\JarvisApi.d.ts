import YObject = require("yunos/core/YObject");
import Page = require("yunos/page/Page");

/**
 * @friend
 */
declare class <PERSON><PERSON><PERSON> extends YObject {
    private _gateway_id;

    constructor(config?: JarvisApi.GatewayConfig);

    destroy(): void;

    sendRequest(service: string,
                method: string,
                param: object | string,
                headers?: {[index: string]: string;},
                http_method?: JarvisApi.HttpMethod,
                options?: JarvisApi.GatewayExtraOption): Promise<JarvisApi.GatewayResponse>;

    request(service: string,
            method: string,
            param: object | string,
            headers?: {[index: string]: string;},
            http_method?: JarvisApi.HttpMethod,
            options?: JarvisApi.GatewayExtraOption): Promise<JarvisApi.GatewayResponse>;


    static request(service: string,
        method: string,
        param: object | string,
        headers?: {[index:string]: string},
        by_get?: boolean): Promise<string>;
    static accessGateway(solutionType: string,
        solutionVersion: string,
        cosmoMsg: object | string, page?: Page): Promise<Object>;
    static accessCosmo(solutionType: string,
        solutionVersion: string,
        cosmoMsg: object | string, page?: Page): Promise<Object>;
    static getDeviceToken(): Promise<Object>;
    static registerDeviceTokenChanged(func: Function): number;
    static unregisterDeviceTokenChanged(handle: Object): void;
    static accessCosmoInsteadUgate(solutionType:string,
        solutionVersion: string,
        cosmoMsg: object | string, func: Function, page?: Page): Promise<Object>;
}

declare namespace JarvisApi {
    enum HttpMethod {
        GET = "GET",
        POST = "POST",
        PUT = "PUT",
        DELETE = "DELETE"
    }
    interface MethodConfig {
        url: string;
        gateway: string;
    }
    interface SolutionTypeConfig {
        name: string;
        gateway: string;
        methods?: MethodConfig[];
    }
    interface GatewayConfig {
        default_gateway?: string;
        solutions?: SolutionTypeConfig[];
    }
    interface KeyValue {
        key: string;
        value: string;
    }
    interface GatewayResponse {
        code: number;
        desc: string;
        data?: string;
        headers?: KeyValue[];
    }

    interface SignByFileOption {
        appkey: string;
    }
    interface GatewayExtraOption {
        appkey?: string;
        timeoutInSeconds?: number;
        uid?: number;
        signByFile?: SignByFileOption;
    }
}

export = JarvisApi;

