import AccountManager = require("./AccountManager");
import Account = require("./Account");
import BaseAuthenticator = require("./BaseAuthenticator");
declare const _default: {
    AccountManager: typeof AccountManager;
    Account: typeof Account;
    AccountManagerConsts: {
        ACCOUNT_CHANGED_BROADCAST: string;
        AUTHENTICATOR_EVENT: string;
        ACCOUNT_CHANGED: string;
        ERROR_MSG: string;
        ERROR_CODE: string;
        USER_CHANGED: string;
        NO_ERROR: number;
        NO_SERVICE: number;
        SERVICE_ERROR: number;
        NO_AUTHENTICATOR: number;
        AUTHENTICATOR_ERROR: number;
        NO_IMPL_AUTHENTICATOR: number;
        EXTERNAL_ERROR: number;
    };
    BaseAuthenticator: typeof BaseAuthenticator;
};
export = _default;
