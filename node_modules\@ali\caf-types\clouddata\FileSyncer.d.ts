import Page = require("yunos/page/Page");
import EventEmitter = require("yunos/core/EventEmitter");
declare class FileSyncer extends EventEmitter {
    private static instance;
    private _nodeCloudData;
    private constructor();
    /**
     * <p>Get instance of FileSyncer by page messages.<br>
     * Use this static method to get FileSyncer instance and do not using new FileSyncer().</p>
     *
     * @public
     */
    static getInstance(pageUri?: string, appName?: string, appObjectName?: string, page?: Page): FileSyncer;
    /**
     * @brief 应用 app 启动需要调用 init api 初始化自身的基本信息。
     * @param uri app page uri.
     * @param appName app 的名字。
     * @param appObjectName appObjectName app 数据在云端存储对象的名字。
     * @returns
     */
    private init;
    pushFile(path: string): Object;
    pullFile(path: string): Object;
    stop(): void;
    destroy(): void;
    private eventListener;
    static readonly EventListenerNotify: {
        FILE_PULL_OK: string;
    };
}
export = FileSyncer;
