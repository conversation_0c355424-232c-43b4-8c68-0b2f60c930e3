/// <reference types="node" />
/**
 * @friend
 * @draft
 */
declare class MTopHelper {
    static readonly SIGN_TOP = "top";
    static readonly SIGN_MTOP = "mtop";
    static readonly SIGN_MTOP_V4 = "mtop_v4";
    static readonly SIGN_FLYSTREET = "flystreet";
    static readonly SIGN_HMACSHA1 = "hmacsha1";
    static readonly SIGN_HMACSHA256 = "hmacsha256";
    static readonly SIGN_XIAMI = "xiami";
    static readonly SIGN_COMMON_MD5 = "commonmd5";
    static readonly SIGN_RECOGNITION = "recognition";
    static readonly ENCRYPT_MODE = "encrypt";
    static readonly DECRYPT_MODE = "decrypt";
    static readonly PARAM_SIGN_TYPE = 0;
    static readonly PARAM_SIGN_INPUT = 1;
    static readonly PARAM_SIGN_IMEI = 2;
    static readonly PARAM_SIGN_IMSI = 3;
    static readonly PARAM_SIGN_ECODE = 4;
    static readonly PARAM_SIGN_DATA = 5;
    static readonly PARAM_SIGN_API = 6;
    static readonly PARAM_SIGN_VERSION = 7;
    static readonly PARAM_SIGN_TIME = 8;
    static readonly PARAM_SIGN_APP_KEY = 9;
    static readonly PARAM_SIGN_SID = 10;
    static readonly PARAM_SIGN_TTID = 11;
    static readonly PARAM_SIGN_DEVICEID = 12;
    static readonly PARAM_SIGN_LAT = 13;
    static readonly PARAM_SIGN_LNG = 14;
    static readonly PARAM_CIPHER_APP_KEY = 15;
    static readonly PARAM_CIPHER_MODE = 16;
    static readonly PARAM_CIPHER_INPUT = 17;
    static readonly PARAM_GET_APP_KEY = 18;
    static sign(params: Map<number, Object>): string;
    static crypt(params: Map<number, Object>, input: Buffer): Buffer;
    static getAppKey(index: Object): string;
    private static process;
}
export = MTopHelper;
