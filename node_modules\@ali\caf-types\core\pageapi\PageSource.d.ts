declare class PageResource {

    getValue(key: string, fileName: string, domain?: string, pageName?: string): Object

    getString(key: string, domain?: string, pageName?: string): string;

    getConfig(key: string, domain?: string, pageName?: string): Object;

    searchFile(fileName: string, domain?: string, pageName?: string): string;

    searchFiles(fileName: string, domain?: string, pageName?: string): string[];

    resetCustomDimension(key: string, value: string): boolean;
}

export = PageResource;
