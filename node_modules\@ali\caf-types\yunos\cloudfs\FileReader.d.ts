import YFileInputStream = require("./FileInputStream");
import FileSystemError = require("./FileSystemError");
/**
 * Class for reading regular files
 * @example
 * let YFileRead = require("yunos/cloudfs/FileReader");
 * let file = new YFile("/data/read_test/file");
 * let readTmp = new YFileRead(file);
 * let reader = new YFileRead(readTmp.getFd());
 * // or use file object directly
 * reader = new YFileRead(new YFile("/data/reader_test/file"));
 * let bytesRead = reader.readSync(buffer, 0, buffer.length);
 * reader.closeSync();
 * @extends yunos.cloudfs.FileInputStream
 * @memberof yunos.cloudfs
 * @public
 * @since 2
 */
declare class FileReader extends YFileInputStream {
    /**
     * Creates a new FileReader object given a file or file descriptor object
     * @param {yunos.cloudfs.File|yunos.cloudfs.FileDescriptor} fileOptObj - The file or fd object
     * @throws {yunos.cloudfs.FileSystemError} If invalid args
     * @public
     * @since 2
     */
    public constructor(fileOptObj: Object);
    /**
     * The function which indicates the result of a async read file to a string
     * @callback yunos.cloudfs.FileReader~readFileCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {string} string - The whole content string read from file
     * @public
     * @since 2
     */
    /**
     * Asynchronous read the entire file's content
     * @param {string} [encoding = "utf8"] encoding - The returned string's encoding
     * @param {yunos.cloudfs.FileReader~readFileCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public readFile(encodingIn: Object, callbackIn?: (err: FileSystemError, data: string) => void): void;
    /**
     * Synchronous read the entire file's content
     * @param {string} [encoding = "utf8"] encoding - The returned string's encoding
     * @return {string} Returns the file's content as string
     * @throws {yunos.cloudfs.FileSystemError} If I/O exception
     * @public
     * @since 2
     */
    public readFileSync(encoding: string): string;
}
export = FileReader;
