import YObject = require("yunos/core/YObject");
import InnerAccount = require("core/account/Account");
/**
 * <p>Value type that represents an Account in the AccountManager.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.account
 * @relyon YUNOS_SYSCAP_ACCOUNT
 * @public
 * @since 2
 */
declare class Account extends YObject {
    private accountId;
    private userName;
    private accountType;
    private extraInfo;
    /**
     * <p>Create Account.<p>
     * @param {string} accountId - the account id.
     * @param {string} userName - the account name.
     * @param {string} accountType - the account type.
     * @public
     * @since 2
     */
    public constructor(accountId: string, userName: string, accountType: string);
    private convert2Inner(): InnerAccount;
}
export = Account;
