
export declare interface PageEvents {
    CREATE: string; //"create",
    START: string; //"start",
    TRIGGER: string; // "trigger",
    STOP: string; // "stop",
    DESTROY: string; // "destroy",
    SUSPEND: string; // "suspend",
    RESUME: string; // "resume",
    LINK: string; // "link",
    TRIM_MEM: string; // "trimMemory",
    FULLGC: string; // "fullGC",
    TOUCH: string; // "touch",
    HOME_KEY: string; // "homeKey",
    PAGE_STATUS: string; // "pageStatus",
    ENVIRONMENT: string; // "environment",
    TIMEZONE: string; // "timezone",
    DEBUG_INFO: string; // "debugInfo",
    COVER_CREATE: string; // "coverCreate",
    ATTACH_PAGE_TOKEN: string; // "attachPageToken",
    CLOSE_CONNECTION: string; // "closeConnection",
    KEYGUARD_UNLOCKED: string; // "keyguardUnlocked"
}

export declare interface PageWindowEvents {
    SHOW: string; //"show",
    HIDE: string; //"hide",
    ACTIVE: string, //"active"
    INACTIVE: string, //"inactive"
    ACTIVATE: string; //"activate",
    DEACTIVATE: string; //"deactivate",
    BACKKEY: string; //"backKey",
    FIRST_FRAME_COMPLETED: string; //"firstFrameCompleted",
    VISIBILITY: string; //"visibility"
}
