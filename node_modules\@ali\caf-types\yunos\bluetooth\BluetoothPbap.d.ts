import EventEmitter = require("../core/EventEmitter");
/**
 * <p>This class provides the APIs to control the Bluetooth PBAP Profile.</p>
 *
 * PBAP client profile allows device to connect to a remote phonebook server,
 * usually a smart phone, to query and download contacts, call logs, speed dials
 * and favorite contacts info.
 *
 * @example
 *
 * const BluetoothPbap = require("yunos/bluetooth/BluetoothPbap");
 * const PbapEvent = BluetoothPbap.EventName;
 * const phone = new BluetoothPbap("3A:4B:11:71:CC:06");
 * const pullContext = {
 *     type: BluetoothPbap.DataType.Phonebook,
 *     offset: 0,
 *     limit: 100,
 *     size: 0,
 * };
 *
 * phone.on(PbapEvent.PbapError, msg => {
 *     console.log(`pbap action failed: ${msg}`);
 * });
 *
 * phone.on(PbapEvent.PbapConnected, state => {
 *     console.log("pbap successfully connected.");
 *     const result = phone.getPhonebookSize(pullContext.type);
 *     if (result !== 0) {
 *         console.log(`failed(${result}) to get phonebook size of `
 *             `${pullContext.type}`);
 *     }
 * });
 *
 * phone.on(PbapEvent.PbapSizeGot, size => {
 *     console.log(`pbap phonebook size got: ${data.size}.`);
 *     pullContext.size = data.size;
 *     const result = phone.pullPhonebook(pullContext.type, pullContext.offset,
 *         pullContext.limit);
 *     if (result !== 0) {
 *         console.log(`failed(${result}) to pull phonebook ${type}`);
 *     }
 * });
 *
 * phone.on(PbapEvent.PbapPulled, data => {
 *     if (pullContext.offset + pullContext.limit >= pullContext.size) {
 *         console.log("done pulling phonebook.");
 *         phone.disconnect();
 *     } else {
 *         console.log(`pulled ${pullContext.offset} ~ `
 *             `${pullContext.offset + pullContext.limit - 1}`);
 *         pullContext.offset += pullContext.limit;
 *         const result = phone.pullPhonebook(pullContext.type, pullContext.offset,
 *             pullContext.limit);
 *         if (result !== 0) {
 *             console.log(`failed(${result}) to pull phonebook ${type}`);
 *         }
 *     }
 * });
 *
 * const result = phone.connect();
 * if (result !== 0) {
 *     console.log(`err(${result}) connect to pbap.`);
 * }
 *
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @permission BLUETOOTH.permission.yunos.com
 * @relyon YUNOS_SYSCAP_BLUETOOTH.PBAP_CLIENT
 * @public
 * @since 5
 */
declare class BluetoothPbap extends EventEmitter {
    private _address;
    private name;
    /**
     * @param {string} address - the mac address of the remote device to connect to.
     */
    public constructor(address: string);
    /**
     * <p>Initiate connection to PBAP server.</p>
     *
     * Device must be connected to a PBAP server before query or download phonebook.
     *
     * @return {number} 0 is success, otherwise is failed.
     * @fires yunos.bluetooth.BluetoothPbap#connected
     * @fires yunos.bluetooth.BluetoothPbap#error
     * @public
     * @since 5
     */
    public connect(): number;
    /**
     * <p>Disconnect from PBAP service.</p>
     *
     * After disconnection, operation such as query or download phonebook
     * becomes invalid.
     *
     * @return {number} 0 is success, otherwise is failed.
     * @fires yunos.bluetooth.BluetoothPbap#disconnected
     * @fires yunos.bluetooth.BluetoothPbap#error
     * @public
     * @since 5
     */
    public disconnect(): number;
    private _pipeConnectionEvents(): void;
    /**
     * <p>Query sizeof specified phonebook. </p>
     *
     * @param {object} type - specify a phonebook.
     * @param {yunos.bluetooth.BluetoothPbap.Location} type.location - location of the phonebook.
     * @param {yunos.bluetooth.BluetoothPbap.DataType} type.dataType - type of the phonebook.
     * @return {number} 0 is success, otherwise is failed.
     * @fires yunos.bluetooth.BluetoothPbap#sizegot
     * @fires yunos.bluetooth.BluetoothPbap#error
     * @public
     * @since 5
     */
    public getPhonebookSize(type: Object): number;
    private _pipeSizeEvent(): void;
    /**
     * <p>Get full phonebook. </p>
     *
     * Get the all contacts/calllogs of specified phonebook/calllog type.
     *
     * @permission BLUETOOTH_PRIVILEGE.permission.yunos.com
     * @param {object} type - specify a phonebook.
     * @param {yunos.bluetooth.BluetoothPbap.Location} type.location - location of the phonebook.
     * @param {yunos.bluetooth.BluetoothPbap.DataType} type.dataType - type of the phonebook.
     * @param {number} offset - start offset.
     * @param {number} count - count of contacts/calllogs starting from offset to download.
     * @return {number} 0 is success, otherwise is failed.
     * @fires yunos.bluetooth.BluetoothPbap#pulled
     * @fires yunos.bluetooth.BluetoothPbap#error
     * @public
     * @since 5
     */
    public pullPhonebook(type: Object, offset: number, count: number): number;
    private _pipePullEvent(): void;
    private _pipeDownloadStatusEvent(): void;
    /**
     * <p>Removes all listeners, or those of the specified eventName for this profile.</p>
     * @param {string} [type] - The name of the event.
     * @override
     * @public
     * @since 5
     */
    public removeAllListeners(type?: string): this;
    private _listenListenerChangeEvents(): void;
    /**
     * <p>Phonebook location</p>
     *
     * Location of the requested phonebook/calllog. Either on device or on SIM.
     *
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly Location: {
        /**
         * Device, phonebook/calllog that is stored on device.
         * @public
         * @since 5
         */
        Device: int;
        /**
         * Sim1, phonebook/calllog that is stored on the first SIM card.
         * @public
         * @since 5
         */
        Sim1: int;
    };
    /**
     * <p>Phonebook type</p>
     *
     * Type of the phonebook/calllog.
     *
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly DataType: {
        /**
         * Contacts
         * @public
         * @since 5
         */
        Phonebook: int;
        /**
         * Incomming calls
         * @public
         * @since 5
         */
        IncomingCallHistory: int;
        /**
         * Outgoing calls
         * @public
         * @since 5
         */
        OutgoingCallHistory: int;
        /**
         * Missed calls
         * @public
         * @since 5
         */
        MissedCallHistory: int;
        /**
         * Combination of incoming/outgoing/missed calls
         * @public
         * @since 5
         */
        CombinedCallHistory: int;
        /**
         * Speed dial info
         * @public
         * @since 5
         */
        SpeedDial: int;
        /**
         * Favorite contacts
         * @public
         * @since 5
         */
        Favorites: int;
    };
    /**
     * <p>Events of PBAP client profile.</p>
     *
     * @enum {string}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly EventName: {
        /**
         * <p>PBAP profile connect result event. </p>
         * @public
         * @since 5
         */
        PbapConnected: string;
        /**
         * <p>PBAP profile disconnect result event. </p>
         * @public
         * @since 5
         */
        PbapDisconnected: string;
        /**
         * <p>PBAP phonebook size report. </p>
         * @public
         * @since 5
         */
        PbapSizeGot: string;
        /**
         * <p>PBAP phonebook detail report. </p>
         *
         * <p>Complete list of contacts/calllogs is saved to data.buffer in vCard2.1
         * format. </p>
         *
         * <p>The contact at index 0 is reserved for device owner card, it always
         * exists.</p>
         *
         * <p>If PHOTO property is included, it is a base64 encoded JPEG picture no larger
         * than 300x300, and its file size will not exceed 50K bytes.</p>
         *
         * @public
         * @since 5
         */
        PbapPulled: string;
        /**
         * <p>PBAP download phonebook status report. </p>
         * @private
         */
        PbapDownloadStatus: string;
        /**
         * <p>PBAP action error. </p>
         *
         * Error happened while processing connect, disconnect, getPhonebookSize or pullPhonebook.
         *
         * @public
         * @since 5
         */
        PbapError: string;
    };
}
export = BluetoothPbap;
