import YObject = require("../core/YObject");
import Page = require("../page/Page");
import FileSystemError = require("./FileSystemError");
/**
 * The main entry for page access its context directories include private and public type.<br>
 * To use this module do 'require("yunos/cloudfs/Context")'.
 * @example
 * let instance = require("yunos/cloudfs/Context").getInstance();
 * let publicDir = instance.getPublicDir();
 * // Here publicDir is "/mnt/data/yunos/public"
 * let picturesDir = context.getPublicDir(Context.PublicDir.Pictures);
 * // Here picturesDir is "/mnt/data/yunos/public/pictures"
 * // Clean current page's cached files in date directory
 * context.cleanPageData(Context.PrivateDir.Cache, function(err, result) {
 *     if (err) {
 *         // Here to handle err
 *         return;
 *     }
 *     // Here clean data successfully
 * });
 * @extends yunos.core.YObject
 * @memberof yunos.cloudfs
 * @public
 * @since 2
 */
declare class Context extends YObject {
    private _uri;
    /**
     * Get the singleton instance
     * @return {yunos.cloudfs.Context} The Context singleton instance
     * @param {yunos.page.page} [page] - The page object for getting uri
     * @public
     * @since 2
     */
    public static getInstance(page?: Page): Context;
    /**
     * Release instance with uri.
     * @example
     * Context.releaseInstance(page);
     * @param {yunos.page.page} [page] - The page object for getting uri
     * @throws {TypyError} If type of uri is not string.
     * @throws {Error} If instance is not exist.
     * @public
     * @since 2
     */
    public static releaseInstance(page: Page): void;
    public constructor(page: Page);
    private getPageType(uri: string): {
        type: string;
    };
    private containerPluginExist(): boolean;
    /**
     * The function which indicates the result of a asynchronous cleaning data
     * @callback yunos.cloudfs.Context~cleanCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @public
     * @since 2
     */
    /**
     * Asynchronous clean the specified page's private data
     * @param {string} [uri] - The page's uri<br>
     * @param {yunos.cloudfs.Context.PrivateDir} [type] - @see {@link yunos.cloudfs.Context.PrivateDir}<br>
     * It represents all the data include local, cache and share by default
     * @param {yunos.cloudfs.Context~cleanCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public cleanPageData(uri: string, type: number, callback: (result: number) => void): void;
    private cleanPageListData(pageList: Page[], type: number, callback: (result: number) => void): void;
    private cleanAllPageData(type: number, callback: (result: number) => void): void;
    /**
     * Asynchronous clean the specified type of public files
     * @permission WRITE_EXTERNAL_STORAGE.permission.yunos.com
     * @param {yunos.cloudfs.Context.PublicDir} [type] - @see {@link yunos.cloudfs.Context.PublicDir}<br>
     * Clean all the public files by default
     * @param {yunos.cloudfs.Context~cleanCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public cleanPublicDir(typeIn: Object, callbackIn?: (err: FileSystemError) => void): void;
    /**
     * Synchronous clean the specified type of public files
     * @permission WRITE_EXTERNAL_STORAGE.permission.yunos.com
     * @param {yunos.cloudfs.Context.PublicDir} [type] - @see {@link yunos.cloudfs.Context.PublicDir}<br>
     * Clean all the public files by default
     * @throws {yunos.cloudfs.FileSystemError} If directory does not exsit or permission denied
     * @public
     * @since 2
     */
    public cleanPublicDirSync(type: number): number;
    /**
     * Get the root path of sepecified page asset
     * @permission [define_permission].permission.yunos.com
     * @param {string} [uri] - The page's uri.<br>
     * It represents current page's uri by default
     * @return {string} Returns the corresponding directory path
     * @public
     * @since 2
     */
    public getAssetDir(uri?: string): string;
    /**
     * The function which indicates the result of a asynchronous getting directory size
     * @callback yunos.cloudfs.Context~getCallback
     * @param {Object} error - The error object that indicates the exception
     * @param {number} size - The corresponding directory's size in KB
     * @public
     * @since 2
     */
    /**
     * Asynchronous get the specified page's asset directory size
     * @permission [define_permission].permission.yunos.com
     * @param {string} [uri] - The page's uri.<br>
     * It represents current page's uri by default
     * @param {yunos.cloudfs.Context~getCallback} callback - The callback when completed
     * @throws {yunos.cloudfs.FileSystemError} if the asset path does not exist
     * @public
     * @since 2
     */
    public getAssetSize(uriIn: Object, callbackIn?: (err: FileSystemError, result: number) => void): void;
    /**
     * Synchronous get the specified page's asset directory size
     * @permission [define_permission].permission.yunos.com
     * @param {string} [uri] - The page's uri.<br>
     * It represents current page's uri by default
     * @return {number} The storage size of the corresponding directory in KB
     * @throws {yunos.cloudfs.FileSystemError} I/O exception
     * @public
     * @since 2
     */
    public getAssetSizeSync(uri: string): number;
    /**
     * Get the absolute path to the cache directory under the current page's private directory.<br>
     * No additional permissions are required for the calling page to read or write files under the returned path
     * @return {string} Returns the corresponding directory path
     * @public
     * @since 2
     */
    public getCacheDir(): string;
    /**
     * Get the absolute path to the local directory under the current page's private directory.<br>
     * No additional permissions are required for the calling page to read or write files under the returned path
     * @return {string} Returns the corresponding directory path
     * @public
     * @since 2
     */
    public getLocalDir(): string;
    private getPageDataSize(uri: string, type: number, callback: (err: number, result: number) => void): void;
    private getPageListDataSize(pageList: Page[], type: number, callback: (err: number, result: number) => void): void;
    private getAllPageDataSize(type: number, callback: (err: number, result: number) => void): void;
    /**
     * Get the directory path of sepecified type of public files
     * @permission WRITE_EXTERNAL_STORAGE.permission.yunos.com
     * @param {string} type - @see {@link yunos.cloudfs.Context.PublicDir}
     * @return {string} Returns the corresponding directory path.<br>
     * Returns the public parent directory path if type by default
     * @public
     * @since 2
     */
    public getPublicDir(type?: number): string;
    /**
     * Get the absolute path of the share directory under the calling page's private directory
     * @permission [define_permission].permission.yunos.com
     * @param {string} uri - The uri string to get the share dir.
     * @return {string} Returns the corresponding directory path
     * @public
     * @since 2
     */
    public getShareDir(uri: string): string;
    /**
     * Reset user id, called after account change
     * @permission READ_ACCOUNT.permission.yunos.com
     * @param no
     * @return no
     * @public
     * @since 6
     */
    public resetUserId(): void;
    private getSizeByDomain(uri: string, callback: (err: number, result: number) => void): void;
    /**
     * Enumeration for public directories. It is designed to store the files for the apps in sdcard_r group.<br>
     * There are some parcular types of file, like media files such as music or vedios.
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly PublicDir: {
        /**
         * Public directory in which to place in the list of alarms that the user can select (not as regular music)
         * @public
         * @since 2
         */
        Alarms: int;
        /**
         * Public directory in which to place pictures that are available to the user
         * @public
         * @since 2
         */
        Pictures: int;
        /**
         * Public directory in which to place any audio files that should be in the regular list of music
         * @public
         * @since 2
         */
        Audios: int;
        /**
         * Public directory in which to place videos that are available
         * @public
         * @since 2
         */
        Videos: int;
        /**
         * Public directory in which to place documents that have been created
         * @public
         * @since 2
         */
        Documents: int;
        /**
         * Public directory in which to place files that have been downloaded
         * @public
         * @since 2
         */
        Downloads: int;
    };
    /**
     * Enumeration for private directories.<br>
     * The private directory includes local, cache and share now.<br>
     * Local directory used to store the data creating by user who using the app.<br>
     * And system don't remove it unless user delete it with promption<br>
     * Cache directory used to store the cached files and system may delete it at any time if needed<br>
     * Share directory used to store the files which users want to share with other apps<br>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly PrivateDir: {
        /**
         * The user data directory
         * @public
         * @since 2
         */
        Local: int;
        /**
         * The cache content directory
         * @public
         * @since 2
         */
        Cache: int;
        /**
         * The share data directory
         * @public
         * @since 2
         */
        Share: int;
        /**
         * All the data directory
         * @public
         * @since 2
         */
        All: int;
    };
    private static readonly OptType: {
        Host: {
            type: string;
        };
        Container: {
            type: string;
        };
        Other: {
            type: string;
        };
    };
    private static readonly Operation: {
        Get: int;
        Clean: int;
        Other: int;
    };
}
export = Context;
