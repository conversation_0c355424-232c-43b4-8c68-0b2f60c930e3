import EventEmitter = require("../core/EventEmitter");
/**
 * <p>This class provides methods to perform advertise related operations for Bluetooth LE devices.</p>
 * <p>An application can start Advertising using AdvertiseData and AdvertiseSettings.</p>
 * @example
 * const BluetoothAdapter = require("yunos/bluetooth/BluetoothAdapter");
 * const btAdapter = BluetoothAdapter.getDefaultAdapter();
 * const advertiser = btAdapter.getLeAdvertiser();
 * const ret = advertiser.startAdvertising(0);
 * console.log("start to advertise, return:", ret);
 *
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @public
 * @since 5
 */
declare class BluetoothLeAdvertiser extends EventEmitter {
    private static _instance: BluetoothLeAdvertiser;
    /** @private */
    private constructor();
    /**
     * register Bluetooth LE Advertisement.
     * @param {yunos.bluetooth.AdvertiseData} AdvertiseData - the advertise data object
     * @param {yunos.bluetooth.AdvertiseSetttings} AdvertiseSetttings - the advertise settings object
     * @return {boolean}  true if command has been issued successfully,false otherwise
     * @public
     * @since 5
     */
    public registerAdvertisement(advertiseData: Object[], advertiseSetttings: Object): number;
    /**
     * unregister Bluetooth LE Advertisement
     * @param {number} client_if - Advertise client id
     * @return {boolean}  true if command has been issued successfully,false otherwise
     * @public
     * @since 5
     */
    public unregisterAdvertisement(client_if: number): number;
    /**
     * start Bluetooth LE Advertisement
     * @param {number} client_if - Advertise client id
     * @return {boolean}  true if command has been issued successfully,false otherwise
     * @public
     * @since 5
     */
    public startAdvertising(client_if: number): number;
    /**
     * stop Bluetooth LE Advertisement
     * @param {number} client_if - Advertise client id
     * @return {boolean}  true if command has been issued successfully,false otherwise
     * @public
     * @since 5
     */
    public stopAdvertising(client_if: number): number;
}
export = BluetoothLeAdvertiser;
