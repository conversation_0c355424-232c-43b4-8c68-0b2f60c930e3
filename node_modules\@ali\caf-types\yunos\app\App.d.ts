import YObject = require("../core/YObject");
import Page = require("../page/Page");
/**
 * <p>Class for Cloud App in YunOS.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.app
 * @public
 * @since 2
 * @hiddenOnPlatform auto
 */
declare class App extends YObject {
    protected _type: string;
    protected _name: string;
    protected _title: string;
    protected _version: string;
    protected _versionCode: number;
    protected _iconPath: string;
    /**
     * <p>Destroy the App class.</p>
     * @public
     * @since 2
     */
    public destroy(): void;
    /**
     * <p>Name of app.</p>
     * @name yunos.app.App#name
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly name: string;
    /**
     * <p>Title of app.</p>
     * @name yunos.app.App#title
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly title: string;
    /**
     * <p>Version of app.</p>
     * @name yunos.app.App#version
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly version: string;
    /**
     * <p>Version code of app.</p>
     * @name yunos.app.App#versionCode
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly versionCode: number;
    /**
     * <p>IconPath of app.</p>
     * @name yunos.app.App#iconPath
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly iconPath: string;
    /**
     * <p>default iconPath of app.</p>
     * @name yunos.app.App#defaultIconPath
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly defaultIconPath: string;
    /**
     * <p>Type of app.</p>
     * @name yunos.app.App#type
     * @type {yunos.app.App.Type}
     * @readonly
     * @public
     * @since 2
     */
    public readonly type: string;
    private getPageList(callback: (err: Object, pages: Page[]) => {}): void;
    private static readonly DefaultIcon = "page://systemres.yunos.com/asset/res/default/default_icon.png";
    /**
     * <p>Enum for Type of app.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Type: {
        /**
         * System app, can not be uninstalled.
         * @public
         * @since 2
         */
        System: string;
        /**
         * System app, updated from origin version.
         * @private
         */
        /**
         * Preload app.
         * @private
         */
        /**
         * Preload app, updated from origin version.
         * @private
         */
        /**
         * Preload app which can not uninstalled.
         * @private
         */
        /**
         * Preload app which can not uninstalled, updated from origin version.
         * @private
         */
        /**
         * Normal app, which can be installed and uninstalled by users.
         * @public
         * @since 2
         */
        Normal: string;
    };
}
export = App;
