import View = require("yunos/ui/view/View");
import Window = require("yunos/ui/view/Window");

declare class PageWindow {
    width: number;
    height: number;
    x: number;
    y: number;
    stackId: number;
    pageWindowAddon: { adjustWindowConfig?: (...args: Object[]) => ObjectReflectI };
    getResolution(deviceId?: number): { width: number, height: number };
    setResolution(resolutionName: string, deviceId: number): Object;
    config: ObjectReflectI;
    /**
     * @friend
     * @draft
     */
    useSystemUI(): void;
    initializeEngineOnly(engine: string): Object;
    /**
     * @friend
     * @draft
     */
    adjustWindowConfig(config: Object): PageWindow.IAdjustedConfig;
    on: Function;
    once: Function;
    removeListener: Function;
    contentView: View;
    firstFrameCompleted: boolean;
    renderingEngine: Object;
    invalidateFastRender: Function;
    stopFastRender: Function;
    getNativeWindowById(id: number): PageWindow.NativeWindow;
    dispatchBackKey: () => void;
    emit: Function;
    backInfo: PageWindow.IBackInfo;
    inactiveReasonInfo: Object;
}

declare namespace PageWindow {
    export interface NativeWindow {
        setLayoutFlags(flags: number): void;
        setOpaqueRegion(opaque: boolean, x: number, y: number, w: number, h: number): void;
        setInputPanelWindowSize(width: number, height: number, displayId: number): void;
        setInputPanelWindowFixedXInDisplay(x: number, displayId: number): void;
        setupAnimation(v: Object): void;
        on(v: string, f: Function): void;
        setParam(v1: number, v2: Object): void;
        release: () => void;
        getZOrder: () => number;
        setWindowReadyToMove(tokenId: string): void;
        setFixScreen(fixed: boolean): void;
        setCloneToDisplay(operation: number, displayId: number): void;
        setWindowPositionInDisplay(tokenId: string, displayId: number, x: number, y: number): void;
        requestCoveredArea(on: boolean): void;
        setPolygonInputRegion(polygonArray: number[]): void;
        setWindowReportAnimationStart(isReport: number): void;
    }
    export interface IAdjustedConfig {
        x: number,
        y: number,
        width: number,
        height: number,
        type: number,
        orientation: number,
        params: number,
        orientationState: number,
        layoutFlags: number
    }
    export interface IBackInfo {
        label: string;
        packageName: string;
        stackId: number;
        taskId: number;
    }
}
export = PageWindow;
