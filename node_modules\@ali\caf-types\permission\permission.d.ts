/*
 * Copyright (C) 2019 Alibaba Group Holding Limited. All Rights Reserved.
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import YObject = require("yunos/core/YObject");
import Ubus = require("ubus");
import Message = Ubus.Message;
import ServiceInterface = Ubus.ServiceInterface;
declare class Permission extends YObject {
    private static _permission;
    static checkPermission(iface: ServiceInterface, perm: string, msg: Message): boolean;
    static inquirePermission(perm: string, callback: (err: number, auth: boolean, data?: Object) => void): void;
    static changePermission(domain: string, perm: string, permOption: number, userId: number): void;
    static getPermissionStatus(domain: string, perm: string, userId: number): number;
    static getPermStatusByUid(uid: number, perm: string): number;
    static getDomainListByPerm(perm: string, callback: (err: number, res: object[]) => void): void;
    static getPermListByDomainSync(domain: string, userId: number): object[];
    static getPermListByDomain(domain: string, userId: number, callback: (err: number, res: object[]) => void): void;
    static getAppList(userId: number, callback: (err: number, p: object[]) => void): void;
    static getUidByDomain(domain: string, userId: number): number;
    static loadPermLog(uid: number, perm: string, callback: (err: number, p: object[]) => void): void;
    static getDomainListByUidSync(uid: number): string[];
    static getDomainListByUid(uid: number, callback: (err: number, p: object[]) => void): void;
    static isSystemApp(uri: string): boolean;
    static isSystemAppByUid(uid: number): boolean;
    static isRemoveable(uri: string): boolean;
    static isRemoveableByUid(uid: number): boolean;
    static isPrivilegedApp(uri: string): boolean;
    static isPrivilegedAppByUid(uid: number): boolean;
    static checkSelfPrivacyPermissionSync(permList: string[]): string;
    static inquirePrivacyPermissions(permList: string[], inquireKey: number, callback: (key: number, err: number, auth: number) => void): void;
    private static onInquirePermissions;
    static changePrivacyPermissions(domain: string, permList: string[], permOption: number, userId: number): void;
    static changePermissions(domain: string, permList: string[], permOption: number, userId: number, flags: number): void;
    static getPrivacyDisplayNames(permList: string[], callback: (p: string[]) => void): void;
    static checkDomainPrivacyPermissions(domain: string, userId: number, privacyPermissionsList: string[]): string;
    static reauthDomainPermissions(domainList: string[], callback: (err: number, res: boolean) => void): void;
    static getReauthDomainPermissionsStatus(): boolean;
    /**
     * <p>Enum for Domain Flag.</p>
     * @enum {number}
     * @readonly
     */
    static readonly DomainFlag: {
        FLAG_DEFAULT: number;
        FLAG_PRE_INSTALL_DOMAIN: number;
        FLAG_NOT_REMOVE: number;
        FLAG_NATIVE_SERVICE: number;
        FLAG_SYSTEM_APP: number;
        FLAG_SHARE_UID: number;
        FLAG_CONTR_APP: number;
        FLAG_PRIVILEGED: number;
    };
    /**
     * <p>Enum for Change Flag.</p>
     * @enum {number}
     * @readonly
     */
    static readonly ChangeFlag: {
        FLAG_CHANGE_DEFAULT: number;
        FLAG_CHANGE_PRIVACY: number;
    };
    /**
     * <p>Enum for Perm Option.</p>
     * @enum {number}
     * @readonly
     */
    static readonly PermOption: {
        PERMISSION_ALLOW: number;
        PERMISSION_DENY: number;
        PERMISSION_ASK: number;
    };
    /**
    * <p>Enum for Perm Dialog Error.</p>
    * @enum {number}
    * @readonly
    */
    static readonly PermDialogError: {
        NOERROR: number;
        DIALOG_EXSIT: number;
    };
    /**
    * <p>Enum for UID.</p>
    * @enum {number}
    * @readonly
    */
    static readonly UID : {
        INVALID: number;
        ROOT: number;
        SYSTEM: number;
        RAIDO: number;
        BLUETOOTH: number;
        GRAPHICS: number;
        INPUT: number;
        AUDIO: number;
        CAMERA: number;
        LOG: number;
        COMPASS: number;
        MOUNT: number;
        WIFI: number;
        ADB: number;
        INSTALL: number;
        MEDIA: number;
        DHCP: number;
        SDCARD_RW: number;
        VPN: number;
        KEYSTORE: number;
        USB: number;
        DRM: number;
        MDNSR: number;
        GPS: number;
        MEDIA_RW: number;
        MTP: number;
        DRMRPC: number;
        NFC: number;
        SDCARD_R: number;
        CLAT: number;
        LOOP_RADIO: number;
        MEDIA_DRM: number;
        PACKAGE_INFO: number;
        SDCARD_PICS: number;
        SDCARD_AV: number;
        SDCARD_ALL: number;
        LOGD: number;
        SHARD_RELRO: number;
        PRIVATE_SHARE: number;
        SYSTEMD_BUS_PROXY: number;
        SHELL: number;
        CACHE: number;
        DIAG: number;
        OEM_RESERVED_START: number;
        OEM_RESERVED_END: number;
        MAP: number;
        ALIXIAOYUN: number;
        ALARM: number;
        APR: number;
        BLUEZ: number;
        BATTERY: number;
        BOOTANIM: number;
        CMNS: number;
        DATAMGR: number;
        NETMGR: number;
        DJANGO: number;
        FC_SET: number;
        FINGERPRINT: number;
        HOSTVSCONTAINER: number;
        IMS: number;
        INPUTMETHOD: number;
        OBEX: number;
        OFONO: number;
        PERMISSION: number;
        PLUGINMGR: number;
        PROVIDERMGR: number;
        SECD: number;
        SEED: number;
        SHTUDOWNANIM: number;
        SYNCMGR: number;
        TELEPHONY: number;
        TKS: number;
        TTS: number;
        UMI: number;
        URLFLT: number;
        UUID: number;
        WESTON: number;
        WESTON_EXTENDSHELL: number;
        WINDOWMGR: number;
        LOCATION: number;
        CNTRDSRV: number;
        CTXAGENTENGINE: number;
        GATELOCK: number;
        SEARCH: number;
        SYSTEMSRV: number;
        VIBRATOR: number;
        JSAOT: number;
        PAGEMGR: number;
        POWERMGR: number;
        CHARGER: number;
        LINKCTRL: number;
        SENSOR: number;
        STORAGE: number;
        WALLPAPER: number;
        WPA: number;
        ASR: number;
        ETHD: number;
        PROPERTYD: number;
        INTELLEGENTSCENED: number;
        MPD: number;
        SPHINXSEARCHD: number;
        AURORA: number;
        INCONDATASRV: number;
        TUNERD: number;
        DEBUG: number;
        SOC: number;
        REMOTE_ASSISTANT: number;
        SGD: number;
        MAGICCUBED: number;
        TEE_SUPPLICANT: number;
        VISION_SERVICE: number;
        XEMU: number;
        CARSRV: number;
        VSOD: number;
        SYSPRIORITY: number;
        CARCAN: number;
        OEM_RESERVED_2_START: number;
        OEM_RESERVED_2_END: number;
        SMARTCARD: number;
        EVERYBODY: number;
        MISC: number;
        NOBODY: number;
        FIRST_CONTR_APP: number;
        LAST_CONTR_APP: number;
    };
    /**
    * <p>Enum for GID.</p>
    * @enum {number}
    * @readonly
    */
    static readonly GID : {
        INVALID: number;
        NET_BT_ADMIN: number;
        NET_BT: number;
        INET: number;
        NET_RAW: number;
        NET_ADMIN: number;
        NET_BW_STATS: number;
        NET_BW_ACCT: number;
        NET_BT_STACK: number;
        READPROC: number;
        WAKELOCK: number;
        SENSORS: number;
        RFS: number;
        RFS_SHARED: number;
    };


}
export = Permission;
