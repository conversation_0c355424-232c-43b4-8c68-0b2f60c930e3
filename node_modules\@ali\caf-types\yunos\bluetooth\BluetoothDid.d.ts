import EventEmitter = require("../core/EventEmitter");
/**
 * <p> This class provides methods to perform get device did information of remote Bluetooth devices. </p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @public
 * @since 5
 */
declare class BluetoothDid extends EventEmitter {
    public constructor();
    private listenListenerChangeEvents(): void;
    private listenBtEvents(): void;
}
export = BluetoothDid;
