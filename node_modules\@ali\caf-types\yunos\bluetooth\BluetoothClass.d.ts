import YObject = require("../core/YObject");
/**
 * <p>Represents a Bluetooth class, which describes general characteristics and capabilities of a
 * device. For example, a Bluetooth class will specify the general device type such as a phone,
 * a computer, or headset, and whether it's capable of services such as audio or telephony.</p>
 *
 * <p>Every Bluetooth class is composed of zero or more service classes, and exactly one device
 * class. The device class is further broken down into major and minor device class components.</p>
 *
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.YObject
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @hiddenOnPlatform auto
 * @public
 * @since 2
 */
declare class BluetoothClass extends YObject {
    private _serviceClasses;
    private _majorDeviceClass;
    private _deviceClass;
    public constructor(deviceMsg: Object);
    /**
     * <p>The device class.</p>
     * @name yunos.bluetooth.BluetoothClass#deviceClass
     * @type {yunos.bluetooth.BluetoothClass.Device}
     * @readonly
     * @public
     * @since 2
     */
    public readonly deviceClass: number;
    /**
     * <p>The major device class.</p>
     * @name yunos.bluetooth.BluetoothClass#majorDeviceClass
     * @type {yunos.bluetooth.BluetoothClass.Device.Major}
     * @readonly
     * @public
     * @since 2
     */
    public readonly majorDeviceClass: number;
    /**
     * <p>The service classes of this bluetooth class.</p>
     * @name yunos.bluetooth.BluetoothClass#serviceClasses
     * @type {yunos.bluetooth.BluetoothClass.Service}
     * @readonly
     * @public
     * @since 2
     */
    public readonly serviceClasses: number;
    private hasService(service: number): number;
    private _doesMatchGeneral(serviceClass: number, deviceClasses: number[]): boolean;
    private doesMatchA2dp(): boolean;
    private doesMatchA2dpSink(): boolean;
    private doesMatchHeadset(): boolean;
    private doesMatchOpp(): boolean;
    private doesMatchHid(): boolean;
    private doesMatchPan(): boolean;
    /**
     * <p>The service class.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Service: {
        /**
         * LIMITED_DISCOVERABILITY
         * @public
         * @since 2
         */
        LIMITED_DISCOVERABILITY: int;
        /**
         * POSITIONING
         * @public
         * @since 2
         */
        POSITIONING: int;
        /**
         * NETWORKING
         * @public
         * @since 2
         */
        NETWORKING: int;
        /**
         * RENDER
         * @public
         * @since 2
         */
        RENDER: int;
        /**
         * CAPTURE
         * @public
         * @since 2
         */
        CAPTURE: int;
        /**
         * OBJECT_TRANSFER
         * @public
         * @since 2
         */
        OBJECT_TRANSFER: int;
        /**
         * AUDIO
         * @public
         * @since 2
         */
        AUDIO: int;
        /**
         * TELEPHONY
         * @public
         * @since 2
         */
        TELEPHONY: int;
        /**
         * INFORMATION
         * @public
         * @since 2
         */
        INFORMATION: int;
    };
    /**
     * <p>The device class.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Device: {
        /**
         * COMPUTER_UNCATEGORIZED
         * @public
         * @since 2
         */
        COMPUTER_UNCATEGORIZED: int;
        /**
         * COMPUTER_DESKTOP
         * @public
         * @since 2
         */
        COMPUTER_DESKTOP: int;
        /**
         * COMPUTER_SERVER
         * @public
         * @since 2
         */
        COMPUTER_SERVER: int;
        /**
         * COMPUTER_LAPTOP
         * @public
         * @since 2
         */
        COMPUTER_LAPTOP: int;
        /**
         * COMPUTER_HANDHELD_PC_PDA
         * @public
         * @since 2
         */
        COMPUTER_HANDHELD_PC_PDA: int;
        /**
         * COMPUTER_PALM_SIZE_PC_PDA
         * @public
         * @since 2
         */
        COMPUTER_PALM_SIZE_PC_PDA: int;
        /**
         * COMPUTER_WEARABLE
         * @public
         * @since 2
         */
        COMPUTER_WEARABLE: int;
        /**
         * PHONE_UNCATEGORIZED
         * @public
         * @since 2
         */
        PHONE_UNCATEGORIZED: int;
        /**
         * PHONE_CELLULAR
         * @public
         * @since 2
         */
        PHONE_CELLULAR: int;
        /**
         * PHONE_CORDLESS
         * @public
         * @since 2
         */
        PHONE_CORDLESS: int;
        /**
         * PHONE_SMART
         * @public
         * @since 2
         */
        PHONE_SMART: int;
        /**
         * PHONE_MODEM_OR_GATEWAY
         * @public
         * @since 2
         */
        PHONE_MODEM_OR_GATEWAY: int;
        /**
         * PHONE_MODEM_OR_GATEWAY
         * @public
         * @since 2
         */
        PHONE_ISDN: int;
        /**
         * AUDIO_VIDEO_UNCATEGORIZED
         * @public
         * @since 2
         */
        AUDIO_VIDEO_UNCATEGORIZED: int;
        /**
         * AUDIO_VIDEO_WEARABLE_HEADSET
         * @public
         * @since 2
         */
        AUDIO_VIDEO_WEARABLE_HEADSET: int;
        /**
         * AUDIO_VIDEO_HANDSFREE
         * @public
         * @since 2
         */
        AUDIO_VIDEO_HANDSFREE: int;
        /**
         * AUDIO_VIDEO_MICROPHONE
         * @public
         * @since 2
         */
        AUDIO_VIDEO_MICROPHONE: int;
        /**
         * AUDIO_VIDEO_LOUDSPEAKER
         * @public
         * @since 2
         */
        AUDIO_VIDEO_LOUDSPEAKER: int;
        /**
         * AUDIO_VIDEO_HEADPHONES
         * @public
         * @since 2
         */
        AUDIO_VIDEO_HEADPHONES: int;
        /**
         * AUDIO_VIDEO_PORTABLE_AUDIO
         * @public
         * @since 2
         */
        AUDIO_VIDEO_PORTABLE_AUDIO: int;
        /**
         * AUDIO_VIDEO_CAR_AUDIO
         * @public
         * @since 2
         */
        AUDIO_VIDEO_CAR_AUDIO: int;
        /**
         * AUDIO_VIDEO_SET_TOP_BOX
         * @public
         * @since 2
         */
        AUDIO_VIDEO_SET_TOP_BOX: int;
        /**
         * AUDIO_VIDEO_HIFI_AUDIO
         * @public
         * @since 2
         */
        AUDIO_VIDEO_HIFI_AUDIO: int;
        /**
         * AUDIO_VIDEO_VCR
         * @public
         * @since 2
         */
        AUDIO_VIDEO_VCR: int;
        /**
         * AUDIO_VIDEO_VIDEO_CAMERA
         * @public
         * @since 2
         */
        AUDIO_VIDEO_VIDEO_CAMERA: int;
        /**
         * AUDIO_VIDEO_CAMCORDER
         * @public
         * @since 2
         */
        AUDIO_VIDEO_CAMCORDER: int;
        /**
         * AUDIO_VIDEO_VIDEO_MONITOR
         * @public
         * @since 2
         */
        AUDIO_VIDEO_VIDEO_MONITOR: int;
        /**
         * AUDIO_VIDEO_VIDEO_DISPLAY_AND_LOUDSPEAKER
         * @public
         * @since 2
         */
        AUDIO_VIDEO_VIDEO_DISPLAY_AND_LOUDSPEAKER: int;
        /**
         * AUDIO_VIDEO_VIDEO_CONFERENCING
         * @public
         * @since 2
         */
        AUDIO_VIDEO_VIDEO_CONFERENCING: int;
        /**
         * AUDIO_VIDEO_VIDEO_GAMING_TOY
         * @public
         * @since 2
         */
        AUDIO_VIDEO_VIDEO_GAMING_TOY: int;
        /**
         * IMAGING_DISPLAY
         * @public
         * @since 2
         */
        IMAGING_DISPLAY: int;
        /**
         * IMAGING_CAMERA
         * @public
         * @since 2
         */
        IMAGING_CAMERA: int;
        /**
         * IMAGING_SCANNER
         * @public
         * @since 2
         */
        IMAGING_SCANNER: int;
        /**
         * IMAGING_PRINTER
         * @public
         * @since 2
         */
        IMAGING_PRINTER: int;
        /**
         * WEARABLE_UNCATEGORIZED
         * @public
         * @since 2
         */
        WEARABLE_UNCATEGORIZED: int;
        /**
         * WEARABLE_WRIST_WATCH
         * @public
         * @since 2
         */
        WEARABLE_WRIST_WATCH: int;
        /**
         * WEARABLE_PAGER
         * @public
         * @since 2
         */
        WEARABLE_PAGER: int;
        /**
         * WEARABLE_JACKET
         * @public
         * @since 2
         */
        WEARABLE_JACKET: int;
        /**
         * WEARABLE_HELMET
         * @public
         * @since 2
         */
        WEARABLE_HELMET: int;
        /**
         * WEARABLE_GLASSES
         * @public
         * @since 2
         */
        WEARABLE_GLASSES: int;
        /**
         * TOY_UNCATEGORIZED
         * @public
         * @since 2
         */
        TOY_UNCATEGORIZED: int;
        /**
         * TOY_ROBOT
         * @public
         * @since 2
         */
        TOY_ROBOT: int;
        /**
         * TOY_VEHICLE
         * @public
         * @since 2
         */
        TOY_VEHICLE: int;
        /**
         * TOY_DOLL_ACTION_FIGURE
         * @public
         * @since 2
         */
        TOY_DOLL_ACTION_FIGURE: int;
        /**
         * TOY_CONTROLLER
         * @public
         * @since 2
         */
        TOY_CONTROLLER: int;
        /**
         * TOY_GAME
         * @public
         * @since 2
         */
        TOY_GAME: int;
        /**
         * HEALTH_UNCATEGORIZED
         * @public
         * @since 2
         */
        HEALTH_UNCATEGORIZED: int;
        /**
         * HEALTH_BLOOD_PRESSURE
         * @public
         * @since 2
         */
        HEALTH_BLOOD_PRESSURE: int;
        /**
         * HEALTH_THERMOMETER
         * @public
         * @since 2
         */
        HEALTH_THERMOMETER: int;
        /**
         * HEALTH_WEIGHING
         * @public
         * @since 2
         */
        HEALTH_WEIGHING: int;
        /**
         * HEALTH_GLUCOSE
         * @public
         * @since 2
         */
        HEALTH_GLUCOSE: int;
        /**
         * HEALTH_PULSE_OXIMETER
         * @public
         * @since 2
         */
        HEALTH_PULSE_OXIMETER: int;
        /**
         * HEALTH_PULSE_RATE
         * @public
         * @since 2
         */
        HEALTH_PULSE_RATE: int;
        /**
         * HEALTH_DATA_DISPLAY
         * @public
         * @since 2
         */
        HEALTH_DATA_DISPLAY: int;
        /**
         * PERIPHERAL_NON_KEYBOARD_NON_POINTING
         * @public
         * @since 2
         */
        PERIPHERAL_NON_KEYBOARD_NON_POINTING: int;
        /**
         * PERIPHERAL_KEYBOARD
         * @public
         * @since 2
         */
        PERIPHERAL_KEYBOARD: int;
        /**
         * PERIPHERAL_POINTING
         * @public
         * @since 2
         */
        PERIPHERAL_POINTING: int;
        /**
         * PERIPHERAL_KEYBOARD_POINTING
         * @public
         * @since 2
         */
        PERIPHERAL_KEYBOARD_POINTING: int;
        /**
        * <p>The major device class.</p>
        * @enum {number}
        * @readonly
        * @public
        * @since 2
        */
        Major: {
            /**
             * MISC
             * @public
             * @since 2
             */
            MISC: int;
            /**
             * COMPUTER
             * @public
             * @since 2
             */
            COMPUTER: int;
            /**
             * PHONE
             * @public
             * @since 2
             */
            PHONE: int;
            /**
             * NETWORKING
             * @public
             * @since 2
             */
            NETWORKING: int;
            /**
             * AUDIO_VIDEO
             * @public
             * @since 2
             */
            AUDIO_VIDEO: int;
            /**
             * PERIPHERAL
             * @public
             * @since 2
             */
            PERIPHERAL: int;
            /**
             * IMAGING
             * @public
             * @since 2
             */
            IMAGING: int;
            /**
             * WEARABLE
             * @public
             * @since 2
             */
            WEARABLE: int;
            /**
             * TOY
             * @public
             * @since 2
             */
            TOY: int;
            /**
             * HEALTH
             * @public
             * @since 2
             */
            HEALTH: int;
            /**
             * UNCATEGORIZED
             * @public
             * @since 2
             */
            UNCATEGORIZED: int;
        };
    };
}
export = BluetoothClass;
