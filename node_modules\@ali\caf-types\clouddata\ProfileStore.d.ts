import EventEmitter = require("yunos/core/EventEmitter");
declare class ProfileStore extends EventEmitter {
    private static instance;
    private _nodeCloudData;
    private constructor();
    /**
     * <p>Get instance of ProfileStore by page messages.<br>
     * Use this static method to get ProfileStore instance and do not using new ProfileStore().</p>
     *
     * @public
     */
    static getInstance(): ProfileStore;
    private createResponse;
    private createProfileResponse;
    private init;
    setAppInfo(appName: string, filePath: string): ProfileStore.Response;
    loadData(callback: (value: ProfileStore.Response) => void): void;
    getValue(key: string): ProfileStore.Response;
    setValue(key: string, value: string, accountId?: string): ProfileStore.Response;
    setPushStrategy(strategy: ProfileStore.PushStrategy): ProfileStore.Response;
    pushDataAsync(callback: (value: string) => void): ProfileStore.Response;
    pushData(timeout: number): ProfileStore.Response;
    pullData(timeout: number): ProfileStore.Response;
    pullDataAsync(callback: (data: string) => void): ProfileStore.Response;
    preLoadLocalConfig(filePath: string): ProfileStore.Response;
    private eventListener;
    static readonly EventListenerNotify: {
        PROFILE_STORE_UPDATE: string;
    };
    static readonly notifyReason: {
        ACCOUNT_CHANGE: string;
        NEW_DATA: string;
    };
    stop(): void;
    destroy(): void;
}
declare namespace ProfileStore {
    class Profile {
        items: Item[];
        account: UserInfo;
    }
}
declare namespace ProfileStore {
    class Item {
        id: string;
        value: any;
        updateTime: number;
        extend: string;
    }
}
declare namespace ProfileStore {
    class Response {
        code: number;
        msg: string;
        data: Object;
    }
}
declare namespace ProfileStore {
    class PushStrategy {
        operation_count: number;
        timer: number;
    }
}
declare namespace ProfileStore {
    class UserInfo {
        id: string;
        name: string;
        role: string;
    }
}
export = ProfileStore;
