import EventEmitter = require("../core/EventEmitter");
/**
* @memberof yunos.bluetooth
* @relyon YUNOS_SYSCAP_BLUETOOTH
* @permission BLUETOOTH.permission.yunos.com
* @private
*/
declare class BluetoothAvrcpController extends EventEmitter {
    private _btApi;
    private name;
    private _address;
    public constructor(address?: string);
    private handleAvrcpControllerEvents(): void;
    private handleListenerChangeEvents(): void;
    private sendCmd(type: number): boolean;
    private setVolume(volume: number): boolean;
    private getTrackInfo(): Object;
    private setProperty(type: number, value: number): boolean;
    private getProperty(type: number): number;
    private getNowPlayingList(): boolean;
    private playItem(itemIndex: number): boolean;
    private GetImage(handle: string, type: string): boolean;
    private GetThumbnail(handle: string, type: string): boolean;
    private static readonly PlayControlCmd: {
        Play: int;
        Pause: int;
        Stop: int;
        Next: int;
        Previous: int;
        PressFastForward: int;
        ReleaseFastForward: int;
        PressRewind: int;
        ReleaseRewind: int;
    };
    private static readonly MetadataAttributes: {
        Title: string;
        Artist: string;
        Album: string;
        Genre: string;
        TotalTracks: string;
        Number: string;
        Duration: string;
        CoverartHandle: string;
    };
    private static readonly PlayerPropertyType: {
        Equalizer: int;
        Repeat: int;
        Shuffle: int;
        Scan: int;
        Position: int;
        Metadata: int;
        Status: int;
    };
    private static readonly EqualizerStatus: {
        Off: int;
        On: int;
        Invalid: int;
    };
    private static readonly RepeatStatus: {
        ModeOff: int;
        SingleTrack: int;
        AllTrack: int;
        Group: int;
        Invalid: int;
    };
    private static readonly ShuffleStatus: {
        ModeOff: int;
        AllTrack: int;
        Group: int;
        Invalid: int;
    };
    private static readonly ScanStatus: {
        ModeOff: int;
        AllTrack: int;
        Group: int;
        Invalid: int;
    };
    private static readonly PlayStatus: {
        Stopped: int;
        Playing: int;
        Paused: int;
        ForwardSeek: int;
        ReverseSeek: int;
        Error: int;
        Invalid: int;
    };
    private static readonly EventName: {
        [key: string]: string;
    };
}
export = BluetoothAvrcpController;
