interface Logger {
    V(...args: any[]): void;

    E(...args: any[]): void;

    I(...args: any[]): void;

    D(...args: any[]): void;

    W(...args: any[]): void;

    trace(...args: any[]): void;
}

interface Log {
    E(TAG: string, ...args: any[]): void;

    I(TAG: string, ...args: any[]): void;

    D(TAG: string, ...args: any[]): void;

    W(TAG: string, ...args: any[]): void;

    V(...args: any[]): void;

    trace(TAG: string, ...args: any[]): void;

    writeBootLog(bootLog: string): void;

    (TAG: string): Logger;

}

declare var log: Log;
declare var processed: Object;
declare var ENABLE_BACKKEY_PROCESS_BY_APP: boolean;
declare module NodeJS {
    export interface Global {
        log: Log
    }
}

declare interface ObjectReflectI {
    [key: string]: Object
}

interface VMTool {
    print(TAG: string, ...args: any[]): void;
}

declare var Vmtool: VMTool;

declare function nativeLoad(path: string): { [key: string]: any };
