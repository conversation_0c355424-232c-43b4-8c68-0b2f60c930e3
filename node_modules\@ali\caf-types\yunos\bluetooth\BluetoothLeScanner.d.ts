import EventEmitter = require("../core/EventEmitter");
/**
 * <p>This class provides methods to perform scan related operations for Bluetooth LE devices.</p>
 * <p>An application can scan for a particular type of Bluetooth LE devices using ScanFilter.</p>
 * @example
 * const BluetoothAdapter = require("yunos/bluetooth/BluetoothAdapter");
 * const BluetoothLeScanner = require("yunos/bluetooth/BluetoothLeScanner");
 * const btAdapter = BluetoothAdapter.getDefaultAdapter();
 * const scanner = adapter.getLeScanner();
 * const filter = {address: watch, deviceName: "MIO GLOBAL"};
 * const scanSettings = {scanMode: BluetoothLeScanner.ScanMode.Opportunistic};
 * const result = scanner.startScan([filter], scanSettings);
 * console.log("start scan return", result);
 *
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @public
 * @since 3
 *
 */
declare class BluetoothLeScanner extends EventEmitter {
    private static _instance: BluetoothLeScanner;
    /** @private */
    private constructor();
    /**
     * The scan filter structure.
     * @typedef {Object} yunos.bluetooth.BluetoothLeScanner~ScanFilter
     * @property {Object} ScanFilter - the object of ScanFilter
     * @property {string} address - the address of the remote device
     * @property {string} name - the name of the remote device
     * @property {string} serviceUuid - the service uuid
     * @public
     * @since 3
     *
     */
    /**
     * Start Bluetooth LE scan.
     * @param {yunos.bluetooth.BluetoothLeScanner~ScanFilter[]} scanFilters - a list of filters
     * @param {Object} scanSettings - the scan settings object
     * @param {yunos.bluetooth.BluetoothLeScanner.ScanMode} - scanSettings.scanMode the scan mode
     * @fires yunos.bluetooth.BluetoothLeScanner#scanresult
     * @public
     * @since 3
     *
     */
    public startScan(scanFilters: Object[], scanSettings: Object): boolean;
    /**
     * Stop Bluetooth LE scan.
     * @public
     * @since 3
     *
     */
    public stopScan(): boolean;
    private listenListenerChangeEvents(): void;
    private listenBtEvents(): void;
    /**
     * The Bluetooth LE scan mode.
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public static readonly ScanMode: {
        /**
         * <p>A special Bluetooth LE scan mode. Applications using this scan mode will passively listen
         * for other scan results without starting BLE scans themselves.</p>
         * @public
         * @since 3
         *
         */
        Opportunistic: int;
        /**
         * <p>Perform Bluetooth LE scan in low power mode. This is the default scan mode as it consumes
         * the least power.</p>
         * @public
         * @since 3
         *
         */
        LowPower: int;
        /**
         * <p>Perform Bluetooth LE scan in balanced power mode. Scan results are returned at a rate that
         * provides a good trade-off between scan frequency and power consumption.</p>
         * @public
         * @since 3
         *
         */
        Balanced: int;
        /**
         * <p>Scan using highest duty cycle. It's recommended to only use this mode when the application
         * is running in the foreground.</p>
         * @public
         * @since 3
         *
         */
        LowLatency: int;
    };
}
export = BluetoothLeScanner;
