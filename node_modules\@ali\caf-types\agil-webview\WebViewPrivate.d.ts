import WebViewClient = require("yunos/web/WebViewClient");

declare class WebViewPrivate {
    constructor(web: Object, viewPrivate: Object);
    destroy: () => void;
    addJavascriptInterface: (object: Object, name: string, functionNames: string[]) => void;
    removeJavascriptInterface: (name: string) => void;
    onUrlChanged: (web: Object, url: string) => void;
    onScrollChanged: (web: Object, x: number, y: number) => void;
    shouldInterceptRequestEnabled: (enabled: boolean) => void;
    getInterface: (objName: string) => Object;
    on: (type: string, event: (...args: Object[]) => void, allowMulticall?: boolean) => void;
    shouldOverrideUrlLoading: (url: string, type: number) => boolean;
    send: (...args: Object[]) => void;
}

export = WebViewPrivate;