"use strict";

const Page = require("yunos/page/Page");
const StackRouter = require("yunos/appmodel/StackRouter");

class App extends Page {

    get theme() {
        return "default";
    }

    onStart() {
        let router = new StackRouter();

        router.container = this.window;

        // route will be register automatically
        // if you follow the automatic module loading mechanism
        // route path -> "home"
        // presenter  -> "src/presenter/HomePresenter.js"
        // view       -> "res/{qualifier}/layout/home.xml"
        // model      -> "src/model/HomeModel.js"
        router.navigate("home");
    }

}

module.exports = App;
