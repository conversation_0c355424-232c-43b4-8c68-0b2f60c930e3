{"name": "@types/node", "version": "8.9.0", "description": "TypeScript definitions for Node.js", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "url": "http://typescriptlang.org"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs", "githubUsername": "parambirs"}, {"name": "<PERSON>", "url": "https://github.com/tellnes", "githubUsername": "tellnes"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker", "githubUsername": "WilcoBakker"}, {"name": "<PERSON>", "url": "https://github.com/octo-sniffle", "githubUsername": "octo-sniffle"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89", "githubUsername": "smac89"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Flarna", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3", "githubUsername": "wwwy3y3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas", "githubUsername": "DeividasBakanas"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin", "githubUsername": "kjin"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis", "githubUsername": "alvis"}, {"name": "<PERSON>", "url": "https://github.com/OliverJAsh", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK", "githubUsername": "Hannes-<PERSON><PERSON>-CK"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno", "githubUsername": "jkomyno"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "1da830639058d7a1c0e61a23ba42b194882cd0afd356b2c5c7e262659a70b6b9", "typeScriptVersion": "2.0", "_id": "@types/node@8.9.0", "dist": {"shasum": "99449266e9f023cc3ad5de304d759de787d18ea4", "size": 60070, "noattachment": false, "key": "@types/node/-/@types/node-8.9.0.tgz", "tarball": "http://registry.npm.alibaba-inc.com/@types/node/download/@types/node-8.9.0.tgz"}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node_8.9.0_1518044680395_0.36922807879352537"}, "_hasShrinkwrap": false, "publish_time": 1518044680468, "_cnpm_publish_time": 1518044680468, "_shasum": "99449266e9f023cc3ad5de304d759de787d18ea4", "_resolved": "http://registry.npm.alibaba-inc.com/@types/node/download/@types/node-8.9.0.tgz", "_from": "@types/node@8.9.0"}