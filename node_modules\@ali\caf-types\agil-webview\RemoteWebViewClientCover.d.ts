import WebViewClient = require("yunos/web/WebViewClient");
import Cover = require("yunos/page/Cover");

declare class RemoteWebViewClientCover extends WebViewClient {
    constructor(parent: Object, cover: Cover);
    /**
     * @override
     * @ignore
     */
    onPageStarted: (self: object, url: string) => void;
    /**
     * @override
     * @ignore
     */
    onPageFinished: (self: object, url: string) => void;
    /**
     * @override
     * @ignore
     */
    onLoadVisuallyCommitted: (self: object, url: string) => void;
    /**
     * @override
     * @ignore
     */
    onReceivedError: (self: object, url: string, reason: number) => void;
    /**
     * @override
     * @ignore
     */
    onDownloadUrl: (self: object, response: Object) => void;
    /**
     * @override
     * @ignore
     */
    shouldOverrideUrlLoading: (self: object, url: string) => boolean;
    /**
     * @override
     * @ignore
     */
    onGeolocationPermission: (webview: object, permission: object) => boolean;
    /**
     * @override
     * @ignore
     */
    _onJSMsgCallback: (name: string, value: Object) => void;
}

export = RemoteWebViewClientCover;
