import YObject = require("../core/YObject");
import Router = require("./Router");
import { RouterInfo } from "./core/define";
/**
 * <p>Context holds the data, router information and config of presenter.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @public
 * @since 3
 *
 */
declare class Context extends YObject {
    private _data;
    private _routeInfo;
    private _router;
    private _launchMode;
    private _config;
    /**
     * <p>Create a context.</p>
     * @param  {Object} [data = null] - Data for presenter.
     * @param  {Object} [routeInfo = null] - Holds the router information when using a router.
     * @param  {Object} [config = null] - Config for presenter.
     * @param  {yunos.appmode.Router} [router = null] - Router for presenter.
     * @param  {string} [router = null] - Launchmode for presenter.
     */
    public constructor(data: Object, routeInfo?: RouterInfo, config?: Object, router?: Router, launchMode?: string);
    /**
     * <p>Hold the data passed by router's navigate api.</p>
     * @name yunos.appmodel.Context#data
     * @type {Object}
     * @default null
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly data: Object;
    /**
     * <p>Hold the route info by current route.</p>
     * @example
     * {
     *     path: "", // original navigate uri
     *     query: "", // query part from the path
     *     match: [] // matched result if current route matched a regexp
     * }
     * @name yunos.appmodel.Context#routeInfo
     * @type {Object}
     * @default null
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly routeInfo: RouterInfo;
    /**
     * @name yunos.appmodel.Context#config
     * @type {Object}
     * @default null
     * @readonly
     * @private
     */
    private readonly config: Object;
    /**
     * <p>Hold the router instance.</p>
     * @name yunos.appmodel.Context#router
     * @type {yunos.appmodel.Router}
     * @default null
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly router: Router;
    /**
     * <p>Hold the launch mode for current navigation.</p>
     * @name yunos.appmodel.Context#launchMode
     * @type {string}
     * @public
     * @since 3
     *
     */
    public launchMode: string;
    private refresh(data: Object, routeInfo: RouterInfo, config: Object, router: Router, launchMode: string): void;
}
export = Context;
