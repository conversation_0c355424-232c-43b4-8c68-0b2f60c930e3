import Presenter = require("./Presenter");
import StackPresenter = require("./StackPresenter");
declare class FloatPagePresenter extends Presenter {
    private _quickControl;
    private _fullFeature;
    private _activePresenter;
    private _mode;
    protected create(): void;
    public mode: number;
    public readonly activePresenter: StackPresenter;
    public readonly quickControl: StackPresenter;
    public readonly fullFeature: StackPresenter;
    public static Mode: {
        QuickControl: int;
        FullFeature: int;
    };
}
export = FloatPagePresenter;
