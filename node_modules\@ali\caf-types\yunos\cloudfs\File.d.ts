import YObject = require("../core/YObject");
import FileSystemError = require("./FileSystemError");
import Uri = require("yunos/net/Uri");
/**
 * The main entry of the file system API module.<br>
 * To use this module do 'require("yunos/cloudfs/File")', most of the methods<br>
 * have asynchronous and synchronous forms. The asynchronous form always takes<br>
 * 'err' as its first argument of callback to tell caller whether if the operation<br>
 * is successful. If the operation was completed successfully, then 'err' will be<br>
 * null or undefined
 * @example
 * let YFile = require("yunos/cloudfs/File");
 * let instance = require("yunos/cloudfs/Context").getInstance();
 * let file = new YFile(instance.getLocalDir(), "text.txt");
 * file.createFile(function(err) => {
 *     if (err) {
 *         console.log("create file failed because of " + err);
 *         return;
 *     }
 *     console.log("create file successfully.");
 * });
 * @extends yunos.core.YObject
 * @memberof yunos.cloudfs
 * @public
 * @since 2
 */
declare class File extends YObject {
    public _path: string;
    /**
     * Creates a new File instance
     * @example
     * let YFile = require("yunos/cloudfs/File");
     * // Three usual ways to create file instance
     * let dir = new YFile("/data/dir");
     * let file1 = new YFile(dir, "file1");
     * let file2 = new YFile("/data/dir", file2);
     * @param {string|yunos.cloudfs.File} [parent] parent - The parent path or file
     * @param {string} path - The given relative path
     * @throws {yunos.cloudfs.FileSystemError} If invalid args
     * @public
     * @since 2
     */
    public constructor(parent: string | File | Uri, path?: string);
    /**
     * The function which indicates the result of scanning directory recursively
     * @callback yunos.cloudfs.File~scanDirCallback
     * @param {string[]} [path] - The array of all the files and dirs, give null if there is no files
     * @public
     * @since 2
     */
    /**
     * Asynchronous scanning directory recursively
     * @param {yunos.cloudfs.File~scanDirCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public scanDir(callback: (result: string[]) => void): void;
    /**
     * Synchronous scanning directory recursively
     * @return {string[]} Returns all the files and dirs under the path
     * @public
     * @since 2
     */
    public scanDirSync(): string[];
    private chmod(mode: number, callback: (err: FileSystemError) => void): void;
    private chmodSync(mode: number): void;
    /**
     * The function which indicates the result of a async create directory
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @callback yunos.cloudfs.File~createDirCallback
     * @public
     * @since 2
     */
    /**
     * Asynchronous create a new, empty directory only if the directory does not yet exist
     * @param {yunos.cloudfs.File~createDirCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public createDirectory(callback: (err: FileSystemError) => void): void;
    /**
     * Synchronous create a new, empty directory only if a directory does not yet exist
     * @throws {yunos.cloudfs.FileSystemError} If the directory exists or I/O exception
     * @public
     * @since 2
     */
    public createDirectorySync(): number;
    /**
     * The function which indicates the result of a async create file operation
     * @callback yunos.cloudfs.File~createFileCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {number} fd - The file descriptor
     * @public
     * @since 2
     */
    /**
     * Asynchronous create a new, empty file only if the file does not yet exist
     * @param {yunos.cloudfs.File~createFileCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public createFile(callback: (err: FileSystemError) => void): void;
    /**
     * Synchronous create a new, empty file only if the file does not yet exist
     * @throws {yunos.cloudfs.FileSystemError} If the file exists or I/O exception
     * @public
     * @since 2
     */
    public createFileSync(): void;
    /**
     * Asynchronous create a symbolic link which points to another file or directory
     * @param {yunos.cloudfs.File|string} target - The target file or directory
     * @param {yunos.cloudfs.File~genericCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public createLink(target: Object, callback: (err: FileSystemError) => void): void;
    /**
     * Synchronous create a symbolic link which points to another file or directory
     * @param {yunos.cloudfs.File|string} target - The target file or directory
     * @throws {yunos.cloudfs.FileSystemError} If the file exists or I/O exception
     * @public
     * @since 2
     */
    public createLinkSync(target: File): void;
    /**
     * The function which indicates the result of file exsits operation
     * @callback yunos.cloudfs.File~existsCallback
     * @param {boolean} result - The result with either true or false
     * @public
     * @since 2
     */
    /**
     * Asynchronous check whether the File entity exists on device
     * @param {yunos.cloudfs.File~existsCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public exists(callback: (result: boolean) => void): void;
    /**
     * Synchronous check whether the File entity exists on device.
     * @return {boolean} Returns true if exists
     * @public
     * @since 2
     */
    public existsSync(): boolean;
    /**
     * The function which indicates the result of getting file mode
     * @callback yunos.cloudfs.File~getModeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {number} result - @see {@link yunos.cloudfs.File.Mode}
     * @public
     * @since 2
     */
    /**
     * Asynchronous check whether the file or directory can be executive, writable, readable
     * @param {yunos.cloudfs.File~getModeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public getMode(callback: (err: FileSystemError) => void): void;
    /**
     * Synchronous check whether the file or directory can be executive, writable, readable
     * @return {number} @see {@link yunos.cloudfs.File.Mode}
     * @throws {yunos.cloudfs.FileSystemError} If I/O exception
     * @public
     * @since 2
     */
    public getModeSync(): number;
    /**
     * The function which indicates the result of getting file modified time
     * @callback yunos.cloudfs.File~modifiedTimeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {Date} date - A Date object reprsents the file's last modified time
     * @public
     * @since 2
     */
    /**
     * Asynchronous get the time that the file was last modified
     * @param {yunos.cloudfs.File~modifiedTimeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public getModifiedTime(callback: (err: FileSystemError, date: Date) => void): void;
    /**
     * Synchronous get the time that the file was last modified
     * @return {Date} a Date object which has time informations
     * @throws {yunos.cloudfs.FileSystemError} If the file doesn't exist or I/O exception
     * @public
     * @since 2
     */
    public getModifiedTimeSync(): Date;
    /**
     * The function which indicates the result of getting file or directory size
     * @callback yunos.cloudfs.File~getSizeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {number} result - The total space size in bytes
     * @public
     * @since 2
     */
    /**
     * Asynchronous get the file or directory total used space in bytes on disk
     * @param {yunos.cloudfs.File~getSizeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public getSize(callback: (err: FileSystemError, result: number) => void): void;
    /**
     * Synchronous get the file or directory total space on disk
     * @return {number} The total space size in bytes
     * @throws {yunos.cloudfs.FileSystemError} If the file dose not exist or I/O exception
     * @public
     * @since 2
     */
    public getSizeSync(): number;
    /**
     * The function which indicates the result of checking file type
     * @callback yunos.cloudfs.File~fileTypeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {boolean} result - The result with either true or false
     * @public
     * @since 2
     */
    /**
     * Asynchronous check whether the path specified when constructing represents a directory
     * @param {yunos.cloudfs.File~fileTypeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public isDirectory(callback: (err: FileSystemError, result: boolean) => void): void;
    /**
     * Synchronous check whether the path specified when constructing represents a directory
     * @return {boolean} Returns true if it's a directory
     * @throws {yunos.cloudfs.FileSystemError} If the file dose not exist or I/O exception
     * @public
     * @since 2
     */
    public isDirectorySync(): boolean;
    /**
     * Asynchronous check whether the path specified when constructing represents a regular file
     * @param {yunos.cloudfs.File~fileTypeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public isFile(callback: (err: FileSystemError, result: boolean) => void): void;
    /**
     * Synchronous check whether the path specified when constructing represents a regular file
     * @return {boolean} Returns true if it's a regular file
     * @throws {yunos.cloudfs.FileSystemError} If the file dose not exist or I/O exception
     * @public
     * @since 2
     */
    public isFileSync(): boolean;
    /**
     * Asynchronous check whether the path specified when constructing represents a symbol link
     * @param {yunos.cloudfs.File~fileTypeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public isLink(callback: (err: FileSystemError, result: boolean) => void): void;
    /**
     * Synchronous check whether the path specified when constructing represents a symbol link
     * @return {boolean} Returns true if it's a symbol link
     * @throws {yunos.cloudfs.FileSystemError} If the file dose not exist or I/O exception
     * @public
     * @since 2
     */
    public isLinkSync(): boolean;
    /**
     * The function which indicates the result of listing the directory
     * @callback yunos.cloudfs.File~listFilesCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {string[]} result - An array of the names of the files in this directory excluding '.' and '..'
     * @public
     * @since 2
     */
    /**
     * Asynchronous list the files in a array which are the children of this file or directory
     * @param {yunos.cloudfs.File~listFilesCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public listFiles(callback: (err: FileSystemError, result: string[]) => void): void;
    /**
     * Synchronous list the files in a array which are the children of this file or directory
     * @return {string[]} Returns an array of the names of the files in the directory excluding '.' and '..'
     * @throws {yunos.cloudfs.FileSystemError} If it's not a directory or the directory dose not exist
     * @public
     * @since 2
     */
    public listFilesSync(): string[];
    /**
     * Asynchronous move the file or directory to the dest file or path
     * @param {yunos.cloudfs.File|string} destFile - The new file instance or path will move to
     * @param {yunos.cloudfs.File~genericCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public moveTo(destFile: Object, callback: (err: FileSystemError) => void): void;
    /**
     * Synchronous move the file or directory to the dest file or path
     * @param {yunos.cloudfs.File|string} destFile - The new file instance or path will move to
     * @throws {yunos.cloudfs.FileSystemError} If the file or directory dose not exist or I/O exception
     * @public
     * @since 2
     */
    public moveToSync(destFile: Object): void;
    /**
     * Get the file's absolute path
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly path: string;
    /**
     * Get the parent file whose path is the parent of this file's path
     * @type {yunos.cloudfs.File}
     * @public
     * @since 2
     */
    public readonly parent: File;
    /**
     * The function which indicates the result of reading link
     * @callback yunos.cloudfs.File~readLinkCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {string} result - The result of reading link
     * @public
     * @since 2
     */
    /**
     * Asynchronous read its target value only if this file is a symbolic link
     * @param {yunos.cloudfs.File~readLinkCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public readLink(callback: (err: FileSystemError, result: string) => void): void;
    /**
     * Synchronous read its target value only if this file is a symbolic link
     * @return {string} Returns The target value of this link
     * @public
     * @since 2
     */
    public readLinkSync(): string;
    /**
     * Asynchronous remove the file or directory, include all the sub files or directories recursively if "isRecursive" is true
     * @param {boolean} isRecursive - If whether remove recursive if it's a directory
     * @param {yunos.cloudfs.File~genericCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public remove(isRecursive: boolean, callback: (err: FileSystemError) => void): void;
    /**
     * Synchronous remove the file or directory, include all the sub files or directories recursively if "isRecursive" is true
     * @throws {yunos.cloudfs.FileSystemError} If the file or directory dose not exist or I/O exception
     * @public
     * @since 2
     */
    public removeSync(isRecursive?: boolean): void;
    private setModifiedTime(time: Date, callback: (err: FileSystemError) => void): void;
    private setModifiedTimeSync(time: Date): void;
    /**
     * Octaleliterals which reprsents the operation mode when open the file
     * @enum {constants}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly OpenMode: {
        /**
         * The read write mode for opening file
         * @public
         * @since 2
         */
        O_RDWR: number;
        /**
         * The write only mode for opening file
         * @public
         * @since 2
         */
        O_WRONLY: number;
        /**
         * The write truncate  mode for opening file
         * @public
         * @since 2
         */
        O_TRUNC: number;
        /**
         * The write append mode for opening file
         * @public
         * @since 2
         */
        O_APPEND: number;
        /**
         * The create mode for opening file
         * @public
         * @since 2
         */
        O_CREAT: number;
        /**
         * The read only mode for opening file
         * @public
         * @since 2
         */
        O_RDONLY: number;
    };
    /**
     * Octal literals which reprsents the file mode when access the file
     * Maybe the mode is the combination of the values or the single value
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Mode: {
        /**
         * The owner's readable permission
         * @public
         * @since 2
         */
        OwnerReadable: int;
        /**
         * The owner's writable permission
         * @public
         * @since 2
         */
        OwnerWritable: int;
        /**
         * The owner's excutive permission
         * @public
         * @since 2
         */
        OwnerExecutive: int;
        /**
         * The owner's readable, writable and excutive permission
         * @public
         * @since 2
         */
        OwnerRWX: int;
        /**
         * The group's readable permission
         * @public
         * @since 2
         */
        GroupReadable: int;
        /**
         * The group's writable permission
         * @public
         * @since 2
         */
        GroupWritable: int;
        /**
         * The group's excutive permission
         * @public
         * @since 2
         */
        GroupExecutive: int;
        /**
         * The group's readable, writable and excutive permission
         * @public
         * @since 2
         */
        GroupRWX: int;
        /**
         * The other's readable permission
         * @public
         * @since 2
         */
        OtherReadable: int;
        /**
         * The other's writable permission
         * @public
         * @since 2
         */
        OtherWritable: int;
        /**
         * The other's excutive permission
         * @public
         * @since 2
         */
        OtherExecutive: int;
        /**
         * The other's readable, writable and excutive permission
         * @public
         * @since 2
         */
        OtherRWX: int;
    };
}
export = File;
