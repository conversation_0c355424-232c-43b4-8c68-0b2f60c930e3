# Installation
> `npm install --save @types/node`

# Summary
This package contains type definitions for Node.js (http://nodejs.org/).

# Details
Files were exported from https://www.github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node/v8

Additional Details
 * Last updated: Wed, 07 Feb 2018 23:04:27 GMT
 * Dependencies: none
 * Global values: <PERSON><PERSON><PERSON>, NodeJS, SlowBuffer, Symbol, __dirname, __filename, clearImmediate, clearInterval, clearTimeout, console, exports, global, module, process, require, setImmediate, setInterval, setTimeout

# Credits
These definitions were written by Microsoft TypeScript <http://typescriptlang.org>, DefinitelyTyped <https://github.com/DefinitelyTyped/DefinitelyTyped>, <PERSON><PERSON><PERSON> <PERSON> <https://github.com/parambirs>, <PERSON> <https://github.com/tellnes>, <PERSON><PERSON><PERSON> <https://github.com/WilcoBakker>, <PERSON> <https://github.com/octo-sniffle>, <PERSON><PERSON><PERSON><PERSON>. <https://github.com/smac89>, <PERSON><PERSON><PERSON> <https://github.com/Flarna>, <PERSON>z Wiktorczyk <https://github.com/mwiktorczyk>, wwwy3y3 <https://github.com/wwwy3y3>, Deividas Bakanas <https://github.com/DeividasBakanas>, Kelvin Jin <https://github.com/kjin>, Alvis HT Tang <https://github.com/alvis>, Oliver Joseph Ash <https://github.com/OliverJAsh>, Sebastian Silbermann <https://github.com/eps1lon>, Hannes Magnusson <https://github.com/Hannes-Magnusson-CK>, Alberto Schiabel <https://github.com/jkomyno>.
