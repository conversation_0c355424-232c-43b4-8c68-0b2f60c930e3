import EventEmitter = require("../core/EventEmitter");
import BluetoothGattService = require("./BluetoothGattService");
/**
 * <p>Public API for the Bluetooth GATT Profile.</p>
 * <p>This class provides Bluetooth GATT functionality to enable communication with Bluetooth LE devices.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @public
 * @since 3
 *
 */
declare class BluetoothGattClient extends EventEmitter {
    private _address;
    /** @private */
    private constructor(address: string);
    /**
     * Connect to the remote device.
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @fires yunos.bluetooth.BluetoothGattClient#connectionchanged
     * @public
     * @since 3
     *
     */
    public connect(): boolean;
    /**
     * Disconnect to the remote device.
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @fires yunos.bluetooth.BluetoothGattClient#connectionchanged
     * @public
     * @since 3
     *
     */
    public disconnect(): boolean;
    /**
     * Discover the services provided by the remote device.
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @fires yunos.bluetooth.BluetoothGattClient#servicediscovered
     * @public
     * @since 3
     *
     */
    public discoverServices(): boolean;
    /**
     * Get the gatt services of the remote device.
     * @return {yunos.bluetooth.BluetoothGattService[]}
     * @public
     * @since 3
     *
     */
    public getServices(): BluetoothGattService[];
    /**
     * Reads the requested characteristic from the associated remote device.
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @param {yunos.bluetooth.BluetoothGattCharacteristic} characteristic - Characteristic to read
     *        from the remote device
     * @fires yunos.bluetooth.BluetoothGattClient#characteristicread
     * @public
     * @since 3
     *
     */
    public readCharacteristic(characteristic: Object): boolean;
    /**
     * Writes a given characteristic and its values to the associated remote device.
     * @param {yunos.bluetooth.BluetoothGattCharacteristic} characteristic - Characteristic to write
     *        to the remote device
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @public
     * @since 3
     *
     */
    public writeCharacteristic(characteristic: Object): boolean;
    /**
     * Enable or disable notifications/indications for a given characteristic.
     * @param {yunos.bluetooth.BluetoothGattCharacteristic} characteristic - The characteristic
     *        for which to enable notifications
     * @param {boolean} enabled - Set to true to enable notifications/indications
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @fires yunos.bluetooth.BluetoothGattClient#characteristicnotify
     * @public
     * @since 3
     *
     */
    public setCharacteristicNotify(characteristic: Object, enabled: boolean): boolean;
    /**
     * Reads the value for a given descriptor from the associated remote device.
     * @param {yunos.bluetooth.BluetoothGattDescriptor} descriptor - Descriptor value to read from
     *        the remote device
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @fires yunos.bluetooth.BluetoothGattClient#descriptorread
     * @public
     * @since 3
     *
     */
    public readDescriptor(descriptor: Object): boolean;
    /**
     * Write the value of a given descriptor to the associated remote device.
     * @param {yunos.bluetooth.BluetoothGattDescriptor} descriptor - Descriptor to write to the
     *        associated remote device
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @public
     * @since 3
     *
     */
    public writeDescriptor(descriptor: Object): boolean;
    /**
     * Read the RSSI for a connected remote device.
     * @return {boolean} true if command has been issued successfully,false otherwise
     * @fires yunos.bluetooth.BluetoothGattClient#rssiread
     * @public
     * @since 3
     *
     */
    public readRssi(): boolean;
    private _listenBtEvents(): void;
}
export = BluetoothGattClient;
