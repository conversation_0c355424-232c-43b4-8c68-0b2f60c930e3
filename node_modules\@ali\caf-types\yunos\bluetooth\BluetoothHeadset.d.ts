import BluetoothProfile = require("./BluetoothProfile");
/**
 * <p>This class provides the APIs to control the Bluetooth Headset Profile.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.bluetooth.BluetoothProfile
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @private
 */
declare class BluetoothHeadset extends BluetoothProfile {
    /** @private */
    private constructor();
    private audioConnected(): boolean;
    private connectAudio(): boolean;
    private disconnectAudio(): boolean;
    private notifyPhoneState(phoneState: Object): boolean;
    private notifyDeviceState(deviceState: Object): boolean;
    private notifyScoVolume(scoVolume: number): boolean;
    private notifyClccResponse(clcc: Object): boolean;
    private static readonly CallState: {
        Active: int;
        Held: int;
        Dialing: int;
        Alerting: int;
        Incoming: int;
        Waiting: int;
        Idle: int;
    };
}
export = BluetoothHeadset;
