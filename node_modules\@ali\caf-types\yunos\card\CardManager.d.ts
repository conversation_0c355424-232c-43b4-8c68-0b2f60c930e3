/// <reference types="node" />
import EventEmitter = require("../core/EventEmitter");
import vm = require("vm");
import View = require("../ui/view/View");
/**
 * <p>Main class for card markup xml to ui.</p>
 * @extends EventEmitter
 * @memberof yunos.card
 * @relyon YUNOS_SYSCAP_CARD
 * @public
 * @since 2
 * @hiddenOnPlatform auto
 */
declare class CardManager extends EventEmitter {
    private static sandbox;
    /**
     * <p>Create an CardManager.</p>
     * @constructor
     * @public
     * @since 2
     */
    /**
     * <p>Destroy this CardManager.</p>
     * @method destroy
     * @public
     * @since 2
     */
    /**
     * <p> Load cml and data to create card view</p>
     * @param  {string} cml - cml string
     * @param  {Object} data - card data
     * @param  {Object} options - options
     * @param  {Object} [options.sandBox] - Defines the sandBox where the script run.<br>
     * If your script want to use some special classes, you can add these classes to sandBox, otherwise they will be undefined<br>
     * @return {view}   card view
     * @public
     * @since 2
     */
    public static loadSync(cml: string, data: Object, options?: {
        sandBox?: vm.Context;
        domain?: string;
        uiVersion?: number;
    }): View;
}
export = CardManager;
