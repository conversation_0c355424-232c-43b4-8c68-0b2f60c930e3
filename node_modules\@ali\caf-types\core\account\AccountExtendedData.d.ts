import YObject = require("yunos/core/YObject");
/**
 * AccountExtendedData class, a data fomat to pass some params to service
 * When new a AccountExtendedData, there is a key and a value in constructor
 * @class AccountExtendedData
 * @param {String} key
 * @param {String} value
 */
declare class AccountExtendedData extends YObject {
    private key: string;
    private value: string;
    constructor(key: string, value: string);
}
export = AccountExtendedData;
