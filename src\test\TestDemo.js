"use sound";
"use strict";
const assert = require("yunos/test/plover/Assert");// eslint-disable-line
const TestCase = require("yunos/test/plover/TestCase");
class TestDemo extends TestCase {
    /* @overwrite */
    beforeAll() {
        this.all = 1;
    }
    /* @overwrite */
    afterAll() {
    }
    /* @overwrite */
    beforeEach() {
        this.each = this.all;
    }
    /* @overwrite */
    afterEach() {
    }
    testCase1() {
        assert.equal(this.all, this.each);
        this.all++;
        assert.equal(this.each, 1);
    }
    testCase2() {
        assert.equal(this.each, 2);
        assert.equal(this.all, this.each);
    }
}
module.exports = TestDemo;
