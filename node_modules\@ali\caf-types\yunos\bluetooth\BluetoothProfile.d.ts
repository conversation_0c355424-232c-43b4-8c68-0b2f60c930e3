import EventEmitter = require("../core/EventEmitter");
import bluetooth = require('bluetooth');
import BluetoothDevice = require("./BluetoothDevice");
/**
 * <p>The base class for all bluetooth profiles, like headset, a2dp, hid etc.</p>
 * <p>You can get specific profile with {@link yunos.bluetooth.BluetoothProfile.getProfile},
 * or get all supported profiles with {@link yunos.bluetooth.BluetoothProfile.getProfiles}.</p>
 *
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @public
 * @since 2
 */
declare class BluetoothProfile extends EventEmitter {
    /**
     * @public
     * @hiddenOnPlatform auto
     */
    public _btApi: bluetooth;
    private _name;
    private code;
    public constructor(name: string);
    /**
     * <p>Get the specified profile with profile name.</p>
     * @param {yunos.bluetooth.BluetoothProfile.Name} name - the name of a profile.
     * @return {yunos.bluetooth.BluetoothProfile} the profile instance.
     * @public
     * @since 2
     */
    public static getProfile(name: string): BluetoothProfile;
    /**
     * <p>Get all supported profiles.</p>
     * @return {yunos.bluetooth.BluetoothProfile[]} an array of profiles.
     * @public
     * @since 2
     */
    public static getProfiles(): BluetoothProfile[];
    /**
     * <p>The name of this profile instance.</p>
     * @name yunos.bluetooth.BluetoothProfile#name
     * @type {yunos.bluetooth.BluetoothProfile.Name}
     * @readonly
     * @public
     * @since 2
     */
    public readonly name: string;
    /**
     * <p>Initiate connection to a profile of the remote bluetooth device.</p>
     * <p>Notice: Pls first trigger a2dp_sink connection,  then system will connect other profiles of
     * avrcp & handsfree_client & pbap_client automatically. Pls don't connect other profiles separately(exclude pbap client).</p>
     * @param {string} address - the mac address of the remote device to connect to.
     * @return {boolean} false on immediate error, true otherwise.
     * @fires yunos.bluetooth.BluetoothProfile#connectionstatechanged
     * @public
     * @since 2
     */
    public connect(address: string): boolean;
    /**
     * <p>Initiate disconnection to a profile of the remote bluetooth device.</p>
     * <p>Notice: Pls first trigger a2dp_sink disconnection,  then system will disconnect other profiles of
     * avrcp & handsfree_client & pbap_client automatically. Pls don't disconnect other profiles separately(exclude pbap client).</p>
     * @param {string} address - the mac address of the remote device.
     * @return {boolean} false on immediate error, true otherwise.
     * @fires yunos.bluetooth.BluetoothProfile#connectionstatechanged
     * @public
     * @since 2
     */
    public disconnect(address: string): boolean;
    /**
    * <p>Get bluetooth devices which have this profile got connected.</p>
    * @return BluetoothDevice[]
    * @public
    * @since 2
    */
    public getConnectedDevices(): BluetoothDevice[];
    /**
     * <p>Set the priority of the profile, the device should already be paired.</p>
     * <p>Function was not supported in new lite, hidden it in this realease.</p>
     * @param {string} address - the mac address of the remote device.
     * @param {BluetoothProfile.Priority.On | BluetoothProfile.Priority.Off} priority - the profile priority.
     * @return {boolean} true if priority is set, false on error.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public setPriority(address: string, priority: number): boolean;
    /**
     * <p>Get the priority of the profile.</p>
     * <p>Function was not supported in new lite, hidden it in this realease.</p>
     * @param {string} address - the mac address of the remote device.
     * @return {BluetoothProfile.Priority} priority of the device.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public getPriority(address: string): number;
    /**
     * <p>Get the connection state of current profile of the specified remote bluetooth device.</p>
     * @param {yunos.bluetooth.BluetoothDevice}  device - the remote bluetooth device.
     * @return {BluetoothDevice.ConnectionState}
     * @public
     * @since 5
     */
    public getConnectionState(device: BluetoothDevice): string;
    private static getCode(name: string): int;
    /**
     * <p>Removes all listeners, or those of the specified eventName for this profile.</p>
     * @param {string} [type] - The name of the event.
     * @override
     * @public
     * @since 5
     */
    public removeAllListeners(type?: string): this;
    private listenListenerChangeEvents(): void;
    /**
     * <p>Device Auto Connect priority for each profile.</p>
     * @enum
     * @readonly
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public static readonly Priority: {
        /**
        * <p>Priority of undefned.</p>
        * @public
        * @since 5
        */
        Undefined: int;
        /**
        * <p>Priority of off.</p>
        * @public
        * @since 5
        */
        Off: int;
        /**
        * <p>Priority of on.</p>
        * @public
        * @since 5
        */
        On: int;
        /**
        * <p>Priority of AutoConnect.</p>
        * @public
        * @since 5
        */
        AutoConnect: int;
    };
    /**
     * <p>Profile types.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Name: {
        /**
         * headset profile, used to make phone call etc.
         * @public
         * @since 2
         */
        HEADSET: string;
        /**
         * the client side of HandsFree profile
         * @public
         * @since 5
         */
        HFP_CLIENT: string;
        /**
         * the sink side of bluetooth music streaming
         * @public
         * @since 5
         */
        A2DP_SINK: string;
        /**
         * bluetooth music streaming support
         * @public
         * @since 2
         */
        A2DP: string;
        /**
         * file transfer profile
         * @public
         * @since 2
         */
        OPP: string;
        /**
         * bluetooth keyboard etc
         * @public
         * @since 2
         */
        HID: string;
        /**
         * bluetooth tethering
         * @public
         * @since 2
         */
        PAN: string;
        /**
         * bluetooth phonebook
         * @public
         * @since 4
         */
        PBAP_CLIENT: string;
        /**
         * bluetooth ble gatt
         * @public
         * @since 4
         */
        GATT: string;
    };
}
export = BluetoothProfile;
