import PageInstance = require("core/pageapi/PageInstance");
import {Service, ServiceInterface} from "ubus";


declare class PageService {
    _service: Service;
    _iface: ServiceInterface;
    constructor(pageapi: PageInstance, busName: string, busPath: string);
    emitEvent(msg: Object): Object;
    addMethod(method: string, handler: (...args: Object[]) => void): void;
    createEventMessage(eventName: string, dest: string, perm: string): Object;
    destroy(): void;
    getClientCount(): number;
    checkPermission(permName: string, msg: Object): boolean;
}
export = PageService;
