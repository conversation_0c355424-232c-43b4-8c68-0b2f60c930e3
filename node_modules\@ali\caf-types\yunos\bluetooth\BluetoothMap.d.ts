import EventEmitter = require("../core/EventEmitter");
/**
 * <p>This class provides the APIs to control the Bluetooth Map Profile.</p>
 *
 * MAP profile allows device to connect to a remote phonebook server,
 * usually a smart phone, to pull message or send message.
 *
 * @example
 *
 * const BluetoothMap = require("yunos/bluetooth/BluetoothMap");
 * const MapEvent = BluetoothMap.EventName;
 * const phone = new BluetoothMap("3A:4B:11:71:CC:06");
 *
 * phone.on(MapEvent.MapError, msg => {
 *     console.log(`map action failed: ${msg}`);
 * });
 *
 * phone.on(MapEvent.MapConnected, function() {
 *     console.log(`map successfully connected.`);
 *     const result = phone.sendMessage("10086", "hello!");
 *     if (result < 0) {
 *         console.log(`failed(${result}) to send message`);
 *     } else {
 *         console.log(`message sent with recorded id $(result)`)
 *     }
 * });
 *
 * phone.on(MapEvent.MapMessageSent, messageid => {
 *     console.log(`send message  $(data.messageid) success`);
 * });
 *
 *
 * const result = phone.connect();
 * if (result !== 0) {
 *     console.log(`err(${result}) connect to pbap.`);
 * }
 *
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @permission BLUETOOTH.permission.yunos.com
 * @relyon YUNOS_SYSCAP_BLUETOOTH.MAP_CLIENT
 * @public
 * @since 5
 * @hiddenOnPlatform auto
 */
declare class BluetoothMap extends EventEmitter {
    private _address;
    private name;
    /**
     * @param {string} address - the mac address of the remote device to connect to.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public constructor(address?: string);
    /**
     * <p>Initiate connection to Map server.</p>
     *
     * <p>Device must be connected to a Map Server before pull/push message</p>.
     *
     * @return {boolean} false on immediate error, true otherwise.
     * @fires yunos.bluetooth.BluetoothMap#connected
     * @fires yunos.bluetooth.BluetoothMap#error
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public connect(): boolean;
    /**
     * <p>Disconnect from Map service.</p>
     *
     * <p>After disconnection, operation pull/push message
     * becomes invalid</p>.
     *
     * @return {boolean} false on immediate error, true otherwise.
     * @fires yunos.bluetooth.BluetoothMap#disconnected
     * @fires yunos.bluetooth.BluetoothMap#error
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public disconnect(): boolean;
    private _pipeConnectionEvents(): void;
    /**
     * <p>send sms message through remote device.</p>
     *
     * @param {string} recipient - the phone number of sms message receiver
     * @param {string} body - the text body of sms message
     * @return {int} <=0 on immediate error.
     *               > 0 success, return a message id which is allocated by service
     * @fires yunos.bluetooth.BluetoothMap#messagesent
     * @fires yunos.bluetooth.BluetoothMap#error
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public sendMessage(recipient: string, body: string): number;
    private _pipeSendMessageEvent(): void;
    private _listenListenerChangeEvents(): void;
    /**
     * Events of Map profile.
     * @enum {string}
     * @readonly
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public static readonly EventName: {
        /**
         * <p>MAP profile connect result event.</p>
         * @public
         * @since 5
         * @hiddenOnPlatform auto
         */
        MapConnected: string;
        /**
         * <p>MAP profile disconnect result event.</p>
         * @public
         * @since 5
         * @hiddenOnPlatform auto
         */
        MapDisconnected: string;
        /**
         * <p>MAP message sent result event.</p>
         * @public
         * @since 5
         * @hiddenOnPlatform auto
         */
        MapMessageSent: string;
        /**
         * <p>MAP action error.</p>
         *
         * <p>Error happened while processing connect, disconnect, sendmessage</p>.
         *
         * @public
         * @since 5
         * @hiddenOnPlatform auto
         */
        MapError: string;
    };
}
export = BluetoothMap;
