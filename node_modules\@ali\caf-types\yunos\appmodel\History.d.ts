import EventEmitter = require("../core/EventEmitter");
/**
 * <p>History provides the interface to manipulate the session history which is the presenters visited.</p>
 * @example
 * let history = new History();
 * history.push("a"); // current "a"
 * history.push("b"); // current "b"
 * history.push("c"); // current "c"
 * history.back();    // current "b"
 * history.forward(); // current "c"
 * @extends yunos.core.EventEmitter
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @private
 */
declare class History extends EventEmitter {
    public attached: boolean;
    private __history__;
    private _currentPosition;
    private isBackable(): boolean;
    /**
     * <p>Get the number of session history.</p>
     * @name yunos.appmodel.History#length
     * @type {number}
     * @default 0
     * @readonly
     * @private
     */
    private readonly length: number;
    /**
     * <p>Get current session.</p>
     * @name yunos.appmodel.History#current
     * @type {string}
     * @default 0
     * @readonly
     * @private
     */
    private readonly current: string;
    /**
     * <p>Get records from the history.</p>
     * @name yunos.appmodel.History#records
     * @type {array}
     * @default []
     * @readonly
     * @private
     */
    private readonly records: string[];
    private save(): void;
    private push(path: string, silent?: boolean): void;
    private replace(path: string, silent?: boolean): void;
    private go(offset: number, silent?: boolean): void;
    private forward(): void;
    private back(): void;
    private clear(): void;
    private notify(path: string, direction?: number): void;
    private static readonly DIRECTION: {
        BACK: int;
        FORWARD: int;
    };
}
export = History;
