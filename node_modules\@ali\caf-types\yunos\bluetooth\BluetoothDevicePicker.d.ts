/**
 * @memberof yunos.bluetooth
 * @private
 */
export declare const LaunchAction = "yunos.action.PICK_BLUETOOTH_DEVICE";
/**
 * @memberof yunos.bluetooth
 * @private
 */
export declare const FilterKey = "filter_key";
/**
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @private
 */
export declare const FilterType: {
    /** @private */
    All: int;
    /** show BT devices that support AUDIO profiles
     * @private
     */
    Audio: int;
    /** show BT devices that support Object Transfer
     * @private
     */
    Transfer: int;
    /** show BT devices that support Personal Area Networking User (PANU) profile
     * @private
    */
    PANU: int;
    /** show BT devices that support Network Access Point (NAP) profile
     * @private
     */
    NAP: int;
};
