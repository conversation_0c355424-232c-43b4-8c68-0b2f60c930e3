import YObject = require("../core/YObject");
/**
 * <p>Class for PageInfo in YunOS.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.app
 * @public
 * @since 2
 * @hiddenOnPlatform auto
 */
declare class PageInfo extends YObject {
    private _name;
    private _icon;
    private _version_code;
    private _version;
    private _title;
    private _main;
    private _type;
    /**
     * <p>Destroy the PageInfo class.</p>
     * @public
     * @since 2
     */
    public destroy(): void;
    /**
     * <p>name of PageInfo.</p>
     * @name yunos.app.PageInfo#name
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly name: string;
    /**
     * <p>icon of PageInfo.</p>
     * @name yunos.app.PageInfo#icon
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly icon: string;
    /**
     * <p>title of PageInfo.</p>
     * @name yunos.app.PageInfo#title
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly title: string;
    /**
     * <p>version of PageInfo.</p>
     * @name yunos.app.PageInfo#version
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly version: string;
    /**
     * <p>versionCode of PageInfo.</p>
     * @name yunos.app.PageInfo#versionCode
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly versionCode: number;
    /**
     * <p>type of PageInfo. PAGE_CLOUD_PINNED(0x11), PAGE_CLOUD_CACHED(0x12)</p>
     * @name yunos.app.PageInfo#type
     * @type {number}
     * @readonly
     * @private
     */
    private readonly type: number;
}
export = PageInfo;
