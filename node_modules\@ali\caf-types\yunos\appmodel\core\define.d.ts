import Presenter = require("../Presenter");
export interface RouterInfo {
    path: string;
    query: Object;
    match: string[];
}
export interface Events {
    [key: string]: {
        [key: string]: (...args: Object[]) => void;
    };
}
export interface EventBound {
    id: string;
    eventName: string;
    handler: () => void;
}
export interface AnimationConfig {
    [key: string]: Object;
}
export interface MaplikeObjectType {
    [key: string]: Object;
}
export declare type AnimationType = Object;
export interface StackItem {
    instance: Presenter;
    uuid: string;
    clz: Object;
    path: string;
}
export declare type StackType = StackItem[];
export declare type PresenterConfigDes = {
    [key: string]: string | number;
};
export declare type PresenterConfigType = {
    presenterGetter: Object;
    config: PresenterConfigDes;
    type: Object;
    uuid: string;
};
