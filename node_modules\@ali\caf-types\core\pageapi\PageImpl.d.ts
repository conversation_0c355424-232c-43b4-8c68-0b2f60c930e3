
import { DomainInfoOptions, PageOptions, PageInfoOptions,
    RecordInfoOptions, SubscribePageInfoOptions, PageCoverOptions, UpdatePageEventInfos
} from "core/pageapi/PageImplInterface";
import Window = require("yunos/ui/view/Window");
import {PageWindowEvents} from "./PageUtils";
import PageInstance = require("core/pageapi/PageInstance");
import Page = require("yunos/page/Page");
import PageLink = require("yunos/page/PageLink");
import PageResource = require("./PageSource");

declare class PageImpl {
    domainInfo: {[key: string]: Object};
    domainName: string;
    resource: PageResource;
    mainWindow: Window;
    domainId: string;
    Uris: ObjectReflectI;
    emulator: Object;
    displayId: number;
    PageWindowEvents: PageWindowEvents;
    instance: PageInstance;
    static getInstance(): PageImpl;
    isRemoveable(x: string): boolean;
    getDomainInfo(uri: string, cb: (err: string, result: DomainInfoOptions) => void): void;
    getDomainInfoList(x: (err: string, results: DomainInfoOptions[]) => void): void;
    getDomainInfoList(x: (err: number, pageList: Page[]) => void): void;
    getPageList(opt: Object, callback: Function) : void;
    getPageInfo(uri: string, cb: (err: string, result: PageInfoOptions) => void): void;
    updatePage(appPackagePath: string, cb: (err: string) => void): void;
    invalidatePage(appName: string, cb: (err: string) => void): void;
    updateOverlay(overlayPath: string, cb: (err: string) => void): void;
    invalidateOverlay(appName: string, cb: (err: string) => void): void;
    loadSubPackage(domain: string, subPackageName: string, callback: (err: string) => void): void;
    resolvePage(pl: Object, cb: (err: string, res: PageInfoOptions[]) => void): void;
    subscribePage(options: Object, cb: (err: string) => void): void;
    unsubscribePage(uri: string, cb: (err: string) => void): void;
    getSubscribedPageList(x: string, cb: (err: string, res: SubscribePageInfoOptions[]) => void): void;
    getPageCover(uri: string, cb: (err: string, result: PageCoverOptions) => void): void;
    getPageCoverList(cb: (err: string, results: PageCoverOptions[]) => void): void;
    getPageStatus(uri: string, callback: (err: Object, status: string) => void): void;
    getPageStatusByUri(uri: string, callback: (err: Object, status: string) => void): void;
    getPageStatusByUriSync(uri: string, timeout: number): string;
    getPageSizeInfo(uri: string, callback: (err: number, result: string) => void): void;
    deletePageCacheFiles(uri: string, callback: (result: number) => void) : void;
    createStack: Function;
    getRunningPages: Function;
    stopRunningPage(pageId: string, param?: number): void;
    sendLink(plink: PageLink): void;
    getCurrentHomeshell(): ObjectReflectI;
    getCurrentHomeshellByDisplayName(displayName: string, callback: (err: Object, homeInfo: Object[]) => void): void;
    switchHomeshellByName(name: string, callback: (err: Object, result: boolean) => void) : void;
    homeKeyPressed(targetDisplayName?: string): void;
    backPage(pageId: string, option?: number): void;
    getLogicalDisplayInfoByDisplayId(displayId: number, callback: (err: Object, displayInfo: Object[]) => void): void;
    getLogicalDisplayInfoByName(displayName: string, callback: (err: Object, displayInfo: Object[]) => void): void;
    getDomainNameByPid(pid: number, callback: (err: Object, result: string) => void) : void;
    getDefaultDisplayName(callback: (err: Object, result: string) => void) : void;
    subscribeAppMgrEvent(event: string, callback: (err: Object, info:UpdatePageEventInfos) => void) : Object;
    unsubscribeAppMgrEvent(handle: Object) : boolean;
    StopParam: {[key: string]: number}
    getRecentTasks(size: number, flag: number, callback: (err: Object, result: Object[]) => void) : void;
    getTopPageByDisplay(displayName: string, callback: (err: Object, result: Object) => void): void;
    getPageInstanceInfoByPid(pid: number, callback: (err: Object, pageInfos: Object[]) => void): void;
    updatePageResource(option: Object[], callback: (err: Object, result: boolean) => void): void;
}
export = PageImpl;
