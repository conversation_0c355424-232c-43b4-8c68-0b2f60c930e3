import EventEmitter = require("../core/EventEmitter");
/**
 * The bluetooth socket used on server side for listening connection requests from client.
 * The interface for Bluetooth Sockets is similar to that of TCP sockets in node.js: `net.Server`
 * and `net.Socket`.
 * On the server side, create a `BluetoothServerSocket` object with a name and a uuid, then call `listen()` to
 * start listen with that uuid. and listen to 'connection' event on the server socket object, when
 * there's new connection, a new connected `BluetoothSocket` object is sent with the event. use this
 * new socket object to communicate with the client.
 *
 * @example
 * const uuid = "uuid-fake";
 * const serviceName = "serviceName";
 * const serverSocket = new BluetoothServerSocket(serviceName, uuid);
 * serverSocket.on("connection", socket => {
 *     console.info("new connection", socket);
 * });
 * serverSocket.on("error", err => {
 *     console.error("error", err);
 * });
 * serverSocket.listen();
 *
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @public
 * @since 2
 * @hiddenOnPlatform auto
 */
declare class BluetoothServerSocket extends EventEmitter {
    private name;
    private uuid;
    private btApi;
    private closing;
    private _listening;
    private listenCalling;
    private onNewConnection;
    /**
     * constructor to create a new BluetoothServerSocket object with the uuid.
     * @param  {string} name - service name for SDP record
     * @param  {string} uuid - uuid for SDP record
     * @return {yunos.bluetooth.BluetoothServerSocket}  the new created BluetoothServerSocket.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public constructor(name: string, uuid: string);
    private _onNewConnection(fd: number, address: string): void;
    /**
     * start listen for client connection requests. 'listening' event will be sent when listen
     * started, and 'error' event will be sent if there's any error.
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public listen(): void;
    /**
     * <p>If the server socket is listening.</p>
     * @name yunos.bluetooth.BluetoothServerSocket#listening
     * @type {boolean}
     * @readonly
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public readonly listening: boolean;
    /**
     * stop listening
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public close(): void;
}
export = BluetoothServerSocket;
