import Presenter = require("./Presenter");
/**
 * <p>A Presenter which attached a stackview by default.</p>
 * @example
 *
 * // implement all home view's presentation logic in this HomePresenter
 * class HomePresenter extends Presenter {
 *     onCreate() {
 *         this.adapter = new HomeAdapter();
 *         this.model = new HomeModel();
 *     }
 *
 *     onViewDetached(listView) {
 *         listView.adapter = this.adapter;
 *     }
 *
 *     onShow() {
 *         this.adapter.data = this.model.update();
 *     }
 *
 *     onViewDetached() {
 *         listView.adapter = null;
 *     }
 *
 *     onDestroy() {
 *         this.adapter.destroy();
 *         this.model.destroy();
 *     }
 * }
 *
 * class MyPage extends Page {
 *     onCreate() {
 *         let stack = new StackPresenter();
 *
 *         let home = new HomePresenter();
 *         home.attachView("home.xml");
 *
 *         let rootView = stack.view;
 *         let window = this.window;
 *
 *         rootView.width = window.width;
 *         rootView.height = window.height;
 *
 *         window.addChild(rootView);
 *
 *         stack.pushChild(home);
 *     }
 * }
 * @extends yunos.appmodel.Presenter
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @private
 */
declare class StackPresenter extends Presenter {
    protected initializePresenter(): void;
    protected create(): void;
    private peekChild(): Presenter;
    private pushChild(presenter: Presenter): void;
    private replaceLastChild(presenter: Presenter): void;
    private _push;
    private popChild(): void;
    private popToChild(presenter: Presenter, purge?: boolean): void;
    private popToRootChild(): void;
    public popToRootChildWithPurge(): void;
    public destroy(): void;
    public back(param: Object): boolean;
    public removeAllChildren(): void;
    public show(): void;
    public hide(): void;
}
export = StackPresenter;
