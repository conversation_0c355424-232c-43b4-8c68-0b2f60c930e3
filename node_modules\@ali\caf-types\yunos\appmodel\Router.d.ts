import Page = require("../page/Page");
import EventEmitter = require("../core/EventEmitter");
import CompositeView = require("../ui/view/CompositeView");
import Presenter = require("./Presenter");
import History = require("./History");
import { StackType, PresenterConfigDes, PresenterConfigType } from "./core/define";
/**
 * <p>Router helps you organize presenters more easily by introducing URL routing and history capabilities.</p>
 * <p>You should use it's subclass like StackRouter for the most of time.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @public
 * @since 3
 */
declare class Router extends EventEmitter {
    protected _presenter: Presenter;
    protected _container: CompositeView;
    protected _stack: StackType[];
    protected _history: History[];
    protected _initRoutePath: string;
    protected _initRouteData: Object;
    protected _initRouteConfig: Object;
    protected _factory: Map<string | RegExp, PresenterConfigType>;
    private _layoutInitByRouter;
    private _shouldRoutePageUri;
    private _shouldDispatchedPageEvent;
    private _singleInstanceCache;
    private _pageEnv;
    private _onNavigate;
    private _rootPath;
    private _config;
    public constructor();
    /**
     * prepare the internal property
     */
    protected initializeRouter(): void;
    /**
     * <p>Holder of views which attahced to a presenter.</p>
     * <p>By default those views will fit container's size if there's no layout.</p>
     * @name yunos.appmodel.Router#container
     * @type {yunos.ui.view.CompositeViewView|null}
     * @throws {TypeError} If not a instance of CompositeViewView.
     * @default null
     * @public
     * @since 3
     *
     */
    public container: CompositeView;
    protected attachHistoryForOnce(history: History): void;
    /**
     * <p>Current presenter.</p>
     * @name yunos.appmodel.Router#presenter
     * @type {yunos.appmodel.Presenter}
     * @readonly
     */
    public readonly presenter: Presenter;
    protected readonly stack: StackType;
    /**
     * <p>Create a route.</p>
     * @param  {string|RegExp} path - A stirng or a regular expression for url matching.
     * @param  {string|yunos.appmodel.Presenter|Function} presenterGetter - presenterGetter.
     * @param  {Object} [config = {}] - Config
     * @public
     * @since 3
     *
     */
    public route(path: string | RegExp, presenterGetter: Object, config?: {
        [key: string]: string | number;
    }): void;
    /**
     * <p>Check a path can be routed or not.</p>
     * @param {string} path - To be checked path.
     * @return {boolean} check result.
     * @public
     * @since 3
     *
     */
    public canRoutePath(path: string, config?: {
        [key: string]: Object;
    }): boolean;
    private execute;
    /**
     * <p>Navigate to a url.</p>
     * @param  {string} path - url.
     * @param  {Object} [data] - Date for target presenter.
     * @param {Object} [newConfig = {}] - Config.
     * @public
     * @since 3
     *
     */
    public navigate(path: string, data?: Object, newConfig?: {}): void;
    /**
     * <p>Replace current presenter with another one.</p>
     * @param {string} path - url.
     * @param {*} [data] - Date for target presenter.
     * @param {Object} [newConfig = {}] - Config.
     * @public
     * @since 3
     *
     */
    public replace(path: string, data?: Object, newConfig?: {}): void;
    private backToIndex(index: number, replacePath?: string, data?: Object, newConfig?: {}): void;
    /**
     * <p>Back.</p>
     * @return {boolean} - Whether it's the last record in the history or not.
     * @public
     * @since 3
     *
     */
    public back(param?: Object): boolean;
    /**
     * <p>Go back to zero-based index with optional uri</p>
     * @param {number} index - target index.
     * @param {string} [replaceUri] - replace url.
     * @param {Object} [data] - Date for target presenter.
     * @param {Object} [newConfig = {}] - Config.
     * @public
     * @since 4
     *
     */
    public backTo(index: number, replaceUri?: string, data?: Object, newConfig?: Object): void;
    /**
     * <p>Remove all Presenter instances from current stack.</p>
     * @public
     * @since 4
     *
     */
    public empty(): void;
    private _empty;
    /**
     * implement this function to add customized config check when
     * getting Presenter
     */
    protected configParser(config: PresenterConfigDes): boolean;
    /**
     * implement this function to add customized parser when using
     * searchPath in auto loader
     */
    protected searchPathParser(originalPath: string): string;
    /**
     * <p>Get presenter.</p>
     */
    protected getPresenter(path: string, data?: Object, newConfig?: Object, direction?: number): Presenter;
    private loadModel;
    protected onNavigate(presenter: Presenter, direction?: number): number;
    private _checkSync;
    protected printHistroy(): void;
    /**
     * <p>Destroy this router.</p>
     * <p>All presenters created by this router will be destroyed.</p>
     * @public
     * @since 3
     */
    public destroy(): void;
    /**
     * <p>Attach a page instance to router so the router can route the page's uri and pass all page's lifecycle events to presenters.</p>
     * @param {yunos.page.Page} - Page instance.
     * @param {boolean} [routePageUri = true] - Router will try to route the page's uri.
     * @param {boolean} [dispatchPageEvent = true] - Dispatch page's lifecycle events or not.
     * @public
     * @since 3
     *
     */
    public run(page: Page, routePageUri?: boolean, dispatchPageEvent?: boolean): void;
    private notify;
    private watch;
    private _registPageEvt;
    private _removeFromStack(presenter: Presenter): void;
    public readonly activatedPresenter: Presenter;
    public readonly initilizedPresenters: StackType;
    public dispatchPageEvent(evt: string, params: Object, uiRelated?: boolean): void;
    /**
     * <p>Set the config for router.</p>
     * <p>When setting the config, please provide one or more from these:</p>
     * {
     *     presenterSearchPath: "",
     *     viewSearchPath: "",
     *     modelSearchPath: ""
     * }
     * @name yunos.appmodel.Router#config
     * @type {Object}
     * @default {}
     * @public
     * @since 4
     *
     */
    public config: {
        [key: string]: Object;
    };
    private readonly rootPath;
    public static DEFAULT_SEARCH_PATH: {
        presenterSearchPath: string;
        viewSearchPath: string;
        modelSearchPath: string;
    };
}
export = Router;
