declare class TKSOption {
    static readonly FLAG_PRIVATE = 1;
    static readonly FLAG_TEMPORARY = 2;
    static readonly FLAG_SECRET = 4;
    static readonly FLAG_FIXED = 8;
    static readonly PERM_MOD_KEY: number;
    static readonly PERM_NEW_KEY: number;
    static readonly PERM_DEL_KEY: number;
    static readonly PERM_GET_KEY: number;
    static readonly PERM_ENCRYPT: number;
    static readonly PERM_DECRYPT: number;
    static readonly PERM_SIGN: number;
    static readonly PERM_VERIFY: number;
    static readonly PERM_STAT_KEY: number;
    static readonly PERM_SAVE_DATA: number;
    static readonly PERM_LOAD_DATA: number;
    static readonly PERM_DEL_DATA: number;
    static readonly PERM_CTRL_KEY: number;
    static readonly PERM_USE_KEY: number;
    static readonly PERM_CTRL_DATA: number;
    private perms;
    private flags;
    private uid;
    private gid;
    private lockLimits;
    private deadTime;
    private tid;
    constructor();
    private setFlags;
    addFlags(flags: number): void;
    clearFlags(flags: number): void;
    private setPerms;
    addSelfPerm(perm: number): void;
    clearSelfPerm(perm: number): void;
    addGroupPerm(perm: number): void;
    clearGroupPerm(perm: number): void;
    addOtherPerm(perm: number): void;
    clearOtherPerm(perm: number): void;
    getSelfPerm(): number;
    size(): number;
}
export = TKSOption;
