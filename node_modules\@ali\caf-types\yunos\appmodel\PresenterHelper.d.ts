import YObject = require("../core/YObject");
import Presenter = require("./Presenter");
/**
 * <p>PresenterHelper.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.appmodel
 * @relyon YUNOS_SYSCAP_APPMODEL
 * @private
 */
declare class PresenterHelper extends YObject {
    private _store;
    public constructor();
    public add(presenter: Presenter): void;
    public remove(presenter: Presenter): void;
    public findPresenterById(id: string): Presenter;
    public hasPresenter(presenter: Presenter): boolean;
    public static getInstance(): PresenterHelper;
}
export = PresenterHelper;
