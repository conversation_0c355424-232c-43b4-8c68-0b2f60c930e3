/// <reference types="node" />
import YObject = require("../core/YObject");
/**
 * <p>Represents a Bluetooth GATT Descriptor.</p>
 * <p>GATT Descriptors contain additional information and attributes of a GATT characteristic.</p>
 * <p>They can be used to describe the characteristic's features or to control certain behaviours
 * of the characteristic.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.YObject
 * @memberof yunos.bluetooth
 * @public
 * @since 3
 *
 */
declare class BluetoothGattDescriptor extends YObject {
    private _data;
    private _value;
    /**
     * Create a new BluetoothGattDescriptor.
     * @param {string} uuid - uuid of this descriptor
     * @param {number} instanceId - the handle of this descriptor
     * @public
     * @since 5
     * @draft
     *
     */
    public constructor(uuid: string, instanceId?: number);
    /**
     * The uuid of this descriptor.
     * @name yunos.bluetooth.BluetoothGattDescriptor#uuid
     * @type {string}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly uuid: string;
    /**
     * The instance id of this descriptor.
     * @name yunos.bluetooth.BluetoothGattDescriptor#instanceId
     * @type {number}
     * @readonly
     * @public
     * @since 5
     *
     */
    public readonly instanceId: number;
    /**
     * The gatt id of this GattDescriptor.
     * @name yunos.bluetooth.BluetoothGattDescriptor#gattId
     * @type {Object} uuid:string, instanceId:number
     * @readonly
     * @public
     * @since 5
     *
     */
    public readonly gattId: {
        uuid: string;
        instanceId?: number;
    };
    /**
     * The value data of this descriptor.
     * @name yunos.bluetooth.BluetoothGattDescriptor#value
     * @type {Buffer}
     * @public
     * @since 3
     *
     */
    /**
    * @private
    */
    public value: Buffer;
    /**
     * <p>Human-readable description.</p>
     * @override
     * @public
     * @since 5
     */
    public toString(): string;
}
export = BluetoothGattDescriptor;
