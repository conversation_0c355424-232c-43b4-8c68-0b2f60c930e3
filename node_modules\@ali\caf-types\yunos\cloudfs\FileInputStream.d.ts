/// <reference types="node" />
import EventEmitter = require("../core/EventEmitter");
import YFile = require("./File");
import YFileDescriptor = require("./FileDescriptor");
import FileSystemError = require("./FileSystemError");
/**
 * Class for reading regular files
 * @example
 * let YFileInputStream = require("yunos/cloudfs/FileInputStream");
 * let file = new YFile("/data/read_test/file");
 * let readTmp = new YFileInputStream(file);
 * let readStream = new YFileInputStream(readTmp.getFd());
 * or use file object directly
 * readStream = new YFileInputStream(new YFile("/data/reader_test/file"));
 * let bytesRead = readStream.readSync(buffer, 0, buffer.length);
 * readStream.closeSync();
 * @extends yunos.core.EventEmitter
 * @memberof yunos.cloudfs
 * @public
 * @since 2
 */
declare class FileInputStream extends EventEmitter {
    public _file: YFile;
    private _fdObj;
    private _position;
    /**
     * Creates a new FileInputStream object given a file or file descriptor object
     * @param {yunos.cloudfs.File|yunos.cloudfs.FileDescriptor} fileOptObj - The file or fd object
     * @throws {yunos.cloudfs.FileSystemError} If invalid args
     * @public
     * @since 2
     */
    public constructor(fileOptObj: Object);
    /**
     * The function which indicates the result of a async close file
     * @callback yunos.cloudfs.FileInputStream~closeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @public
     * @since 2
     */
    /**
     * close file async by fd
     * @param {yunos.cloudfs.FileInputStream~closeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public close(callback: (err: FileSystemError) => void): void;
    /**
     * close file sync by fd
     * @throws {yunos.cloudfs.FileSystemError} If close failed, throw FileSystemError
     * @public
     * @since 2
     */
    public closeSync(): boolean;
    /**
     * return the fd object
     * @throws {yunos.cloudfs.FileSystemError} If fd is not exist
     * @return {yunos.cloudfs.FileDescriptor} Return fd object
     * @public
     * @since 2
     */
    public getFd(): YFileDescriptor;
    /**
     * The function which indicates the result of a async read file to a string
     * @callback yunos.cloudfs.FileInputStream~readCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {number} length - An integer specifying the number of bytes to read
     * @param {Buffer} buffer - A Buffer object that the data will be written to
     * @public
     * @since 2
     */
    /**
     * Asynchronous read data from file to a buffer
     * @param {Buffer} buffer - A Buffer object that the data will be written to
     * @param {number} bufOffset - The offset in the buffer to fill for reading
     * @param {number} length - An integer specifying the number of bytes to read
     * @param {yunos.cloudfs.FileInputStream~readCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public read(buffer: Buffer, bufOffset: number, length: number, callback: (errOut: FileSystemError, bytesRead: number, buffer: Buffer) => void): void;
    /**
     * Synchronous read data from file to a buffer
     * @param {Buffer} buffer - A Buffer object that the data will be written to
     * @param {number} bufOffset - The offset in the buffer to start writing at
     * @param {number} length - An integer specifying the number of bytes to read
     * @return {number} Returns the number of bytes read
     * @throws {yunos.cloudfs.FileSystemError} If I/O exception
     * @public
     * @since 2
     */
    public readSync(buffer: Buffer, bufOffset: number, length: number): number;
}
export = FileInputStream;
