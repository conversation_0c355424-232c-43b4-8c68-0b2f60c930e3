import EventEmitter = require("../core/EventEmitter");
import BluetoothDevice = require("./BluetoothDevice");
import BluetoothLeScanner = require("./BluetoothLeScanner");
import BluetoothLeAdvertiser = require("./BluetoothLeAdvertiser");
/**
 * <p>A bluetooth adapter instance represents a local bluetooth device.
 * Call {@link yunos.bluetooth.BluetoothAdapter.getDefaultAdapter} to get the default adapter instance.</p>
 *
 * <p>This is the starting point for all bluetooth actions, with the adapter instance,
 * you can query if the bluetooth is currently enabled with {@link yunos.bluetooth.BluetoothAdapter#enabled}
 * and change it with {@link yunos.bluetooth.BluetoothAdapter#enable} or {@link yunos.bluetooth.BluetoothAdapter#disable};
 * get all paired bluetooth devices with {@link yunos.bluetooth.BluetoothAdapter#getPairedDevices};
 * scan remote devices with {@link BluetoothAdapter#startScan}.</p>
 * @example
 * const BluetoothAdapter = require("yunos/bluetooth/BluetoothAdapter");
 * const btAdapter = BluetoothAdapter.getDefaultAdapter();
 * const isBtEnabed = btAdapter.enabled;
 * btAdapter.on("devicefound", device => console.log("device found", device));
 * btAdapter.on("scanningchanged", data => console.log("scanning changed", data.scanning));
 * const result = btAdapter.startScan();
 * if (result) console.error("failed to start scan", result);
 *
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.EventEmitter
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @public
 * @since 2
 */
declare class BluetoothAdapter extends EventEmitter {
    private static _defaultAdapter;
    public constructor();
    private static poke(): void;
    /**
     * <p>Returns a BluetoothLeScanner object for LE scan operations.</p>
     * @return {yunos.bluetooth.BluetoothLeScanner} the static BluetoothLeScanner object
     * @public
     * @since 3
     *
     */
    public getLeScanner(): BluetoothLeScanner;
    /**
     * <p>Returns a BluetoothLeAdvertiser object for LE advertise operations.</p>
     * @return {yunos.bluetooth.BluetoothLeAdvertiser} the static BluetoothLeAdvertiser object
     * @public
     * @since 5
     */
    public getLeAdvertiser(): BluetoothLeAdvertiser;
    /**
     * <p>Get the default instance of the BluetoothAdapter.</p>
     * @return {yunos.bluetooth.BluetoothAdapter} the default instance of BluetoothAdapter, it will
     * be null if the device does not support bluetooth.
     * @public
     * @since 2
     */
    public static getDefaultAdapter(): BluetoothAdapter;
    /**
     * <p>destroy relative used resource when construct BluetoothAdapter.</p>
     * @name yunos.bluetooth.BluetoothAdapter#destroyDefaultAdapter
     * @readonly
     * @public
     * @since 5
     */
    public destroy(): void;
    /**
     * <p>The mac address of the local bluetooth device.</p>
     * @name yunos.bluetooth.BluetoothAdapter#address
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly address: string;
    /**
     * <p>the uuids of this device.</p>
     * @name yunos.bluetooth.BluetoothAdapter#uuids
     * @type {string[]}
     * @readonly
     * @public
     * @since 5
     */
    public readonly uuids: string[];
    /**
     * <p>The human-readable name of the local bluetooth device.</p>
     * @name yunos.bluetooth.BluetoothAdapter#name
     * @type {string}
     * @public
     * @since 2
     */
    /**
    * <p>set the human-readable name of the local bluetooth device, it should <= 248 bytes.
    * this is an async operation, event namechanged will be sent once the operation is done.</p>
    * @name yunos.bluetooth.BluetoothAdapter#name
    * @type {string}
    * @throws {Error} when args are invalid, or the operation failed.
    * @fires yunos.bluetooth.BluetoothAdapter#namechanged
    * @public
    * @since 5
    */
    public name: string;
    /**
     * <p>Return true if Bluetooth is currently enabled and ready for use.</p>
     * @name yunos.bluetooth.BluetoothAdapter#enabled
     * @type {boolean}
     * @readonly
     * @public
     * @since 2
     */
    public readonly enabled: boolean;
    /**
     * <p>Turn on the local Bluetooth adapter, this is an async command, event
     * {@link yunos.bluetooth.BluetoothAdapter#enabledchanged} will be sent when successfully turned on.</p>
     * @param {yunos.bluetooth.BluetoothAdapter~requestCallback} callback - called when request is done.
     * @fires yunos.bluetooth.BluetoothAdapter#enabledchanged
     * @permission BT_OPEN.permission.yunos.com
     * @public
     * @since 2
     */
    public enable(callback: (result: boolean) => void): void;
    /**
     * <p>Turn off the local Bluetooth adapter, this is an async command, event
     * {@link yunos.bluetooth.BluetoothAdapter#enabledchanged} will be sent when successfully turned off.</p>
     * @param {yunos.bluetooth.BluetoothAdapter~requestCallback} callback - called when request is done.
     * @fires yunos.bluetooth.BluetoothAdapter#enabledchanged
     * @permission BT_OPEN.permission.yunos.com
     * @public
     * @since 2
     */
    public disable(callback: (result: boolean) => void): void;
    /**
     * <p>The current state of the local bluetooth adapter.</p>
     * @name yunos.bluetooth.BluetoothAdapter#state
     * @type {yunos.bluetooth.BluetoothAdapter.State}
     * @readonly
     * @public
     * @since 2
     */
    public readonly state: string;
    /**
     * <p>The current visibility of the local bluetooth adapter, namely if other bluetooth device can
     * find this one when scanning.</p>
     * @name yunos.bluetooth.BluetoothAdapter#visible
     * @type {boolean}
     * @public
     * @since 2
     */
    /**
    * <p>set the current visibility of the local bluetooth adapter.
    * this is an async operation, event visiblechanged will be sent once the operation is done.</p>
    * @name yunos.bluetooth.BluetoothAdapter#visible
    * @type {boolean}
    * @fires yunos.bluetooth.BluetoothAdapter#visiblechanged
    * @throws {Error} If failed to set visibility.
    * @public
    * @since 5
    */
    public visible: boolean;
    private requestVisible(timeout: number, callback: (result: boolean) => void): void;
    /**
     * <p>The current connectable ablity of the local bluetooth adapter.</p>
     * @name yunos.bluetooth.BluetoothAdapter#connectable
     * @type {boolean}
     * @public
     * @since 6
     */
    /**
    * <p>set the current connectable ablity of the local bluetooth adapter.
    * If set as FALSE, the remote device can't page the local BT,so the incoming pair and connection will be fail.
    * this is an sync operation, event connectable changed will be sent once the operation is done.</p>
    * @name yunos.bluetooth.BluetoothAdapter#connectable
    * @type {boolean}
    * @fires yunos.bluetooth.BluetoothAdapter#connectablechanged
    * @throws {Error} If failed to set connectable.
    * @public
    * @since 6
    */
    public connectable: boolean;
    /**
     * <p>If the local adapter is scanning for other bluetooth devices.</p>
     * @name yunos.bluetooth.BluetoothAdapter#scanning
     * @type {boolean}
     * @readonly
     * @public
     * @since 2
     */
    public readonly scanning: boolean;
    /**
     * <p>start scan for remote bluetooth devices, the new found device is returned via devicefound
     * event.</p>
     * @fires yunos.bluetooth.BluetoothAdapter#devicefound
     * @fires yunos.bluetooth.BluetoothAdapter#scanningchanged
     * @return {number} 0 is for success, otherwise failed
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public startScan(): void;
    /**
     * <p>Stop the on-going scan process.</p>
     * @return {number} 0 is for success, otherwise failed
     * @fires yunos.bluetooth.BluetoothAdapter#scanningchanged
     * @public
     * @since 2
     * @hiddenOnPlatform auto
     */
    public stopScan(): void;
    /**
     * <p>Confirm passkey, used to answer pairing request for PairingVariant.PasskeyConfirmation.</p>
     * @permission BLUETOOTH_PRIVILEGE.permission.yunos.com
     * @param {string} address - the mac address of the remote device.
     * @param {boolean} confirm - true to accept the request, false to reject.
     * @return {boolean} true confirmation has been sent out, false for error.
     * @throws {Error} If invalid args.
     * @public
     * @since 2
     */
    public setPairingConfirmation(address: string, confirm: boolean): boolean;
    /**
     * <p>Set the pin key during pairing for PairingVariant.PinConfirmation. the pin key should <= 16 bytes.</p>
     * @permission BLUETOOTH_PRIVILEGE.permission.yunos.com
     * @param {string} address - the mac address of the remote device.
     * @param {string} pin - the pin key.
     * @return {boolean} true if the pin has been sent, false otherwise.
     * @throws {Error} If invalid args.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setPin(address: string, pin: string): boolean;
    /**
     * <p>Cancel the on-going pairing process with the remote device. after calling this method, there
     * will be pairingstatechanged event with none as the new state.</p>
     * @permission BLUETOOTH_PRIVILEGE.permission.yunos.com
     * @param {string} address - the address of the remote device.
     * @return {boolean} false on error, true otherwise.
     * @throws {Error} If invalid args.
     * @fires yunos.bluetooth.BluetoothAdapter#pairingstatechanged
     * @public
     * @since 5
     */
    public cancelPairing(address: string): boolean;
    /**
     * <p>Start pairing process with the remote bluetooth device identified with address.
     * async api, pairing result is returned by event pairingstatechanged.</p>
     * <p>In the pairing process, event pairingrequest is sent.</p>
     * <p>For main branch, this interface only support BLE pair. BR/EDR is not supported yet by now.</p>
     * @permission BLUETOOTH_PRIVILEGE.permission.yunos.com
     * @param {string} address - the mac address of the remote bluetooth device.
     * @return {boolean} false on immediate error, true if the pairing process begins.
     * @throws {Error} If invalid args.
     * @fires yunos.bluetooth.BluetoothAdapter#pairingrequest
     * @fires yunos.bluetooth.BluetoothAdapter#pairingstatechanged
     * @public
     * @since 2
     */
    public pairDevice(address: string): boolean;
    /**
     * <p>Unpair the paired remote device identified with address.</p>
     * <p>Async api, result is returned by event pairingstatechanged.</p>
     * @permission BLUETOOTH_PRIVILEGE.permission.yunos.com
     * @param {string} address - the mac address of the remote bluetooth device.
     * @return {boolean} false on immediate error, true if unpairing starts.
     * @throws {Error} If invalid args.
     * @fires yunos.bluetooth.BluetoothAdapter#pairingstatechanged
     * @public
     * @since 5
     */
    public unpairDevice(address: string): boolean;
    /**
     * <p>Get all paired remote devices.</p>
     * @return {yunos.bluetooth.BluetoothDevice[]} an array of remote devices.
     * @public
     * @since 2
     */
    public getPairedDevices(): BluetoothDevice[];
    /**
     * <p>Get the BluetoothDevice object for the given address.
     * @param  {string} address - the mac address of a bluetooth device.
     * @return {yunos.bluetooth.BluetoothDevice} a bluetooth device object if found, null otherwise.
     * @public
     * @since 5
     */
    public getRemoteDevice(address: string): BluetoothDevice;
    private setPasskey(address: string, passkey: number): number;
    private setEnabled(enabled: boolean, callback: (result: boolean) => void): void;
    private testMacAddress(address: string): void;
    /**
    * <p>Removes all listeners, or those of the specified eventName.</p>
    * @param {string} [type] - The name of the event.
    * @override
    * @public
    * @since 4
    */
    public removeAllListeners(type: string): this;
    private listenListenerChangeEvents(): void;
    private listenBtEvents(): void;
    /**
     * <p>Local bluetooth adapter status.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly State: {
        /**
         * Turning on
         * @public
         * @since 2
         */
        TurningOn: string;
        /**
         * On
         * @public
         * @since 2
         */
        On: string;
        /**
         * Turning off
         * @public
         * @since 2
         */
        TurningOff: string;
        /**
         * Off
         * @public
         * @since 2
         */
        Off: string;
    };
    private static readonly PairingVariant: {
        PasskeyConfirmation: int;
        PasskeyEntry: int;
        PasskeyConsent: int;
        PasskeyNotification: int;
        PinConfirmation: int;
        PinEntry: int;
        Pin16Digits: int;
        OobConsent: int;
    };
}
export = BluetoothAdapter;
