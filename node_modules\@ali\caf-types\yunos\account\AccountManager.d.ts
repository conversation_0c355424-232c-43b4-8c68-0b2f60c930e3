import YObject = require("yunos/core/YObject");
import Account = require("yunos/account/Account");
/**
 * <p>AccountManager class.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.account
 * @relyon YUNOS_SYSCAP_ACCOUNT
 * @public
 * @since 2
 */
declare class AccountManager extends YObject {
    /**
     * @private
     */
    private constructor();
    /**
     * <p>Get singleton AccountManager instance.</p>
     * @return {yunos.account.AccountManager} the AccountManager instance.
     * @public
     * @since 2
     */
    public static getInstance(): AccountManager;
    /**
     * addAccount callback.
     * @callback yunos.account.AccountManager~addAccountCallback
     * @param {Object} result - Account Service provider could customize its Authorization result
     * @public
     * @since 2
     */
    /**
     * <p>Add for third party app. when this type of account is not login, call login ui.</p>
     * @param {string} accountType - the specified type of accout.
     * @param {string} authenticatorName - the specified auth token type.
     * @param {Object} addAccountOptions - get account token and userdata.
     * @param {yunos.account.AccountManager~addAccountCallback} callback - The callback that handles the response.
     * @public
     * @since 2
     */
    public addAccount(accountType: string, authTokenType: string, addAccountOptions: object, callback: (x: object) => void): void;
    /**
     * getAuthInfo callback.
     * @callback yunos.account.AccountManager~getAuthInfoCallback
     * @param {Object} result - Account Service provider could customize its Authorization result
     * @public
     * @since 2
     */
    /**
     * <p>Gets an authtoken of the specified type for a oarticular account, this method is intended for foreground or background task defined by uiMark.</p>
     * @param {yunos.account.Account} account - the specified account.
     * @param {string} authTokenType - the specified auth token type.
     * @param {Object} options - get account token and userdata.
     * @param {boolean} uiMark - this param is to be continue design...
     * @param {yunos.account.AccountManager~getAuthInfoCallback} callback - The callback that handles the response.
      @public
     * @since 2
     */
    public getAuthInfo(account: Account, authTokenType: string, options: object, uiMark: boolean, callback: (x: object) => void): void;
    /**
     * getAccountByType callback.
     * @callback yunos.account.AccountManager~getAccountByTypeCallback
     * @param {yunos.account.Account[]} accounts - Already logged in accounts of An Account Service provider
     * @public
     * @since 2
     */
    /**
     * <p>Get Accounts by account type for third party app.</p>
     * @param {string} accountType - the account type.
     * @param {yunos.account.AccountManager~getAccountByTypeCallback} callback - the callback that handles the response.
     * @public
     * @since 2
     */
    public getAccountByType(accountType: string, callback: (accounts: Account[]) => void): void;
    private onAccountChanged(accountChangedHandler: (...args: Object[]) => void): void;
    private offAccountChanged(accountChangedHandler: (...args: Object[]) => void): void;
}
export = AccountManager;
