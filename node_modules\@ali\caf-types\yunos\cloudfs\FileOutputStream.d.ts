/// <reference types="node" />
import EventEmitter = require("../core/EventEmitter");
import YFile = require("./File");
import YFileDescriptor = require("./FileDescriptor");
import FileSystemError = require("./FileSystemError");
/**
 * Class for writting regular files
 * @example
 * let YFileOutputStream = require("yunos/cloudfs/FileOutputStream");
 * let file = new YFile("/data/write_test/file");
 * let writeTmp = new FileOutputStream(file);
 * let writeStream = new FileOutputStream(writeTmp.getFd());
 * or use file object directly
 * writeStream = new FileOutputStream(new YFile("/data/write_test/file"));
 * let bytesWrite = writeStream.writeSync(buffer, 0, buffer.length);
 * writeStream.closeSync();
 * @extends yunos.core.EventEmitter
 * @memberof yunos.cloudfs
 * @public
 * @since 2
 */
declare class FileOutputStream extends EventEmitter {
    public _file: YFile;
    private _mode;
    private _position;
    private _fdObj;
    /**
     * Creates a new FileOutputStream object given a file or file descriptor object
     * @param {yunos.cloudfs.File|yunos.cloudfs.FileDescriptor} fileOptObj - The file or fd object
     * @param {yunos.cloudfs.File.OpenMode} mode - The mode for writing
     * @throws {yunos.cloudfs.FileSystemError} If invalid args
     * @public
     * @since 2
     */
    public constructor(fileOptObj: Object, mode?: number);
    /**
     * The function which indicates the result of a async close file
     * @callback yunos.cloudfs.FileOutputStream~closeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @public
     * @since 2
     */
    /**
     * close file async by fd
     * @param {yunos.cloudfs.FileOutputStream~closeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public close(callback: (err: FileSystemError) => void): void;
    /**
     * close file sync by fd
     * @throws {yunos.cloudfs.FileSystemError} If close failed, throw FileSystemError
     * @public
     * @since 2
     */
    public closeSync(): boolean;
    /**
     * return the fd object
     * @throws {yunos.cloudfs.FileSystemError} If fd is not exist
     * @return {yunos.cloudfs.FileDescriptor} Return fd object
     * @public
     * @since 2
     */
    public getFd(): YFileDescriptor;
    /**
     * The function which indicates the result of a asynchronous write a string to file
     * @callback yunos.cloudfs.FileOutputStream~writeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {number} length - is an integer specifying the number of bytes to write
     * @param {Buffer} buffer - The source buffer
     * @public
     * @since 2
     */
    /**
     * Asynchronous write data from buffer to current file
     * @param {Buffer} buffer - The source buffer
     * @param {number} bufOffset - The offset in the buffer to start writing at
     * @param {number} length - is an integer specifying the number of bytes to write
     * @param {yunos.cloudfs.FileOutputStream~writeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public write(buffer: Buffer, bufOffset: number, length: number, callback: (err: FileSystemError, written: number, buffer: Buffer) => void): void;
    /**
     * Synchronous write a buffer to current file
     * @param {Buffer} buffer - The source buffer
     * @param {number} offset - The offset in the buffer to start writing at
     * @param {number} length - An integer specifying the number of bytes to write
     * @return {number} Returns the number of bytes written
     * @throws {yunos.cloudfs.FileSystemError} If I/O exception
     * @public
     * @since 2
     */
    public writeSync(buffer: Buffer, bufOffset: number, length: number): number;
}
export = FileOutputStream;
