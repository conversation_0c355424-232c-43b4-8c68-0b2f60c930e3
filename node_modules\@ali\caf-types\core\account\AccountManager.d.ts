import AccountManagerService = require("./lib/AccountManagerServiceProxy");
import Account = require("./Account");
import EventEmitter = require("yunos/core/EventEmitter");
/**
 * AccountManager class.
 * @class AccountManager
 * @extends EventEmitter
 */
declare class AccountManager extends EventEmitter {
    private _getAccountManagerImpl: () => AccountManagerService;
    constructor();
    /**
     * add Explicitly for account app.
     * #attention please# this api is only for account app.
     * @method AccountManager#addExplicitly
     * @param {Account} account to be added
     * @param {AccountExtendedData[]} userdata of this account
     * @param {AccountManager~addExplicitlyCallback} callback The callback that handles the response.
     * @public
     */
    addExplicitly(account: Account, userdata: object, callback: (p: string) => void): void;
    /**
     * Add for third party app. when this type of account is not login, call login ui.
     * @method AccountManager#add
     * @param {String} accountType specified the type of accout
     * @param {String} authenticatorName the specified auth token type
     * @param {Object} addAccountOptions
     * @param {AccountManager~addCallback} callback The callback that handles the response.
     * @public
     */
    add(accountType: string, authTokenType: string, addAccountOptions: object, callback: (p: object) => void): void;
    /**
     * @gets an authtoken of the specified type for a oarticular account, this method is intended for foreground or background task defined by uiMark
     * @method AccountManager#getAuthInfo
     * @param {Account} account the specified accout
     * @param {String} authTokenType the specified auth token type
     * @param {Object} options
     * @param {bool} uiMark this param is to be continue design...
     * @param {AccountManager~getAuthInfoCallback} callback The callback that handles the response.
     * @public
     */
    getAuthInfo(account: Account, authTokenType: string, options: object, uiMark: boolean, callback: (p: object) => void): void;
    /**
     * validate Password.
     * @method AccountManager#validatePassword
     * @param {Account} account
     * @param {String} authenticatorName
     * @param {Object} options
     * @param {AccountManager~validatePasswordCallback} callback The callback that handles the response.
     * @public
     */
    validatePassword(account: Account, authTokenType: string, options: object, callback: (p: object) => void): void;
    /**
     * @description gets an auth info of the specified type for a oarticular account sync
     * @method AccountManager#getAuthInfoSync
     * @param {Account} account the specified accout
     * @param {String} authTokenType the specified auth token type
     * @param {Object} options
     * @returns {Object}
     * @public
     */
    getAuthInfoSync(account: Account, authTokenType: string, options: object): object;
    /**
     * remove account for account app.
     * @method AccountManager#remove
     * @param {Account} account the account to be removed
     * @param {String} authenticatorName
     * @param {bool} uiMark
     * @param {AccountManager~removeCallback} callback
     * @public
     */
    remove(account: Account, authTokenType: string, uiMark: boolean, callback: (p: string) => void): void;
    /**
     * update account for account app.
     * #attention please# this api is only for account app.
     * @method AccountManager#update
     * @param {Account} account the account to be updated
     * @param {String} authenticatorName
     * @param {Object} userdata the account's userdata
     * @param {AccountManager~updateCallback} callback
     * @public
     */
    update(account: Account, authTokenType: string, userdata: object, callback: (p: string) => void): void;
    /**
     * Get Accounts for third party app.
     * @method AccountManager#getAccounts
     * @param {AccountManager~getAccountsCallback} callback
     * @public
     */
    getAccounts(callback: (accounts: Account[], error: {
        name: string;
        message: string;
        type: string;
    }) => void): void;
    /**
     * Get Accounts by account type for third party app.
     * @example
     * var AccountManager = require("core/account/AccountManager"),
     * var accountManager = AccountManager.getInstance();
     * accountManager.getAccountByType("xxx", (accounts) => {
     * });
     * @method AccountManager#getAccountByType
     * @param {String} accountType
     * @param {AccountManager~getAccountByTypeCallback} callback
     * @public
     */
    getAccountByType(accountType: string, callback: (accounts: Account[], error: {
        name: string;
        message: string;
        type: string;
    }) => void): void;
    /**
     * Get current user id.
     * @method AccountManager#getCurrentUserId
     * @param {AccountManager~getCurrentUserId} callback
     * @public
     */
    getCurrentUserId(callback: (p: object, error: {
        name: string;
        message: string;
        type: string;
    }) => void): void;
    /**
     * Get Accounts by account type for third party app sync api.
     * @method AccountManager#getAccountByTypeSync
     * @param {String} accountType
     * @returns {Account[]}
     * @public
     */
    getAccountByTypeSync(accountType: string): object;
    /**
     * Get Accounts by account type and name for third party app.
     * @method AccountManager#getAccountByTypeAndName
     * @param {String} accountType
     * @param {String} userName
     * @param {AccountManager~getAccountByTypeAndNameCallback} callback
     * @public
     */
    getAccountByTypeAndName(accountType: string, userName: string, callback: (account: Account, error: {
        name: string;
        message: string;
        type: string;
    }) => void): void;
    /**
     * Get Accounts by account type and name for third party app.
     * @method AccountManager#getAccountByTypeAndNameSync
     * @param {String} accountType
     * @param {String} userName
     * @returns {Account}
     * @public
     */
    getAccountByTypeAndNameSync(accountType: string, userName: string): object;
    /**
     * Set an account as current account.
     * #attention please# this api is only for account app.
     * @method AccountManager#setCurrentAccount
     * @param {Account} account
     * @param {AccountManager~setCurrentAccountCallback} callback
     * @public
     */
    setCurrentAccount(account: Account, callback: (result: Object, error: {
        name: string;
        message: string;
        type: string;
    }) => void): void;
    /**
     * find account by userId sync api.
     * @method AccountManager#findAccountByUserIdSync
     * @param {Number} userId
     * @param {String} accountType
     * @returns {Account}
     * @public
     */
    findAccountByUserIdSync(userId: number, accountType: string): object;
    /**
     * find account by userId.
     * @method AccountManager#findAccountByUserId
     * @param {Number} userId
     * @param {String} accountType
     * @param {AccountManager~findAccountByUserIdCallback} callback
     * @public
     */
    findAccountByUserId(userId: number, accountType: string, callback: (result: object, error: {
        name: string;
        message: string;
        type: string;
    }) => void): void;
    /**
     * get current account of a type.
     * @method AccountManager#getCurrentAccount
     * @param {String} accountType
     * @param {AccountManager~getCurrentAccountCallback} callback
     * @public
     */
    getCurrentAccount(accountType: string, callback: (account: Account, error: {
        name: string;
        message: string;
        type: string;
    }) => void): void;
    /**
     * get current account of a type sync api.
     * @method AccountManager#getCurrentAccountSync
     * @param {String} accountType
     * @returns {Account}
     * @public
     */
    getCurrentAccountSync(accountType: string): object;
    /**
     * get singleton AccountManager instance
     * @method getInstance
     * @memberOf AccountManager
     * @static
     * @return {AccountManager}
     * @public
     */
    static getInstance(): AccountManager;
}
export = AccountManager;
