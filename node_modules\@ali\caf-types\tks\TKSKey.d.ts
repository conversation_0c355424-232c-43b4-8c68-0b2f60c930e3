/// <reference types="node" />
declare class TKSKey {
    static readonly KEY_FLAG_CRYPT = 256;
    static readonly KEY_AES = 0;
    static readonly KEY_DES = 1;
    static readonly KEY_RSA = 2;
    static readonly KEY_EC = 4;
    static readonly KEY_RSA_PRIVATE: number;
    static readonly KEY_RSA_PUBLIC: number;
    static readonly KEY_EC_PRIVATE: number;
    static readonly KEY_EC_PUBLIC: number;
    protected type: number;
    protected encoded: Buffer;
    constructor(type: number);
    getType(): number;
    getEncoded(): Buffer;
}
declare class TKS<PERSON>ry<PERSON><PERSON><PERSON> extends TKS<PERSON><PERSON> {
    constructor(type: number, enc: Buffer);
}
declare class TKS<PERSON>ES<PERSON><PERSON> extends TKSKey {
    private keySize;
    private key;
    private iv;
    constructor(keySize: number, key: Buffer, iv: Buffer);
}
declare class TKSDE<PERSON><PERSON><PERSON> extends TKSKey {
    private key;
    private iv;
    constructor(key: <PERSON>uffer, iv: <PERSON>uffer);
}
declare class TKS<PERSON><PERSON><PERSON><PERSON> extends TKSKey {
    constructor(type: number, enc: Buffer);
}
declare class TKSEC<PERSON>ey extends TKSKey {
    constructor(type: number, enc: Buffer);
}
export { TKSKey, TKSCryptKey, TKSAESKey, TKSDESKey, TKSRSAKey, TKSECKey };
