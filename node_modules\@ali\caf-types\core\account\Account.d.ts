import YObject = require("yunos/core/YObject");
/**
 * Account class
 * @class Account
 * @param {String} accountId
 * @param {String} userName
 * @param {String} accountType
 * @param {Object} extraInfo
 */
declare class Account extends YObject {
    /**
     * @public
     */
    accountId: string;
    /**
     * @public
     */
    userName: string;
    /**
     * @public
     */
    accountType: string;
    private extraInfo: object;
    constructor(accountId: string, userName: string, accountType: string, extraInfo: object);
}
export = Account;
