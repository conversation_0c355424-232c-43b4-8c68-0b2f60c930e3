import YObject = require("../core/YObject");
import FileSystemError = require("./FileSystemError");
import CloudfsPluginT = require("plugin/cloudfs/CloudfsPlugin");
import Message = CloudfsPluginT.Message;
/**
 * The class is for the scenario transfer file descriptor from another process
 * To use this module do 'require("yunos/cloudfs/FileDescriptor")'
 * @example
 * let YFileDescriptor = require("yunos/cloudfs/FileDescriptor");
 * let YFileReader = require("yunos/cloudfs/FileReader");
 * let YFile = require("yunos/cloudfs/File");
 * let fileReader = new YFileReader(new YFile("/data/existsFile"));
 * let fdObj = fileReader.getFd();
 * let fileRdObj = new YFileReader(fdObj);
 * fileRdObj.readSync(buffer);
 * fileRdObj.closeSync();
 * @extends yunos.core.YObject
 * @memberof yunos.cloudfs
 * @public
 * @since 2
 * @hiddenOnPlatform auto
 */
declare class FileDescriptor extends YObject {
    public _fd: number;
    public _position: number;
    /**
     * Creates a new FileDescriptor instance
     * @param {number} fd - The file descriptor of the opening file
     * @throws {yunos.cloudfs.FileSystemError} If invalid args
     * @private
     */
    private constructor(fd: number);
    private getFd(): number;
    private dupFd(): FileDescriptor;
    /**
     * The function which indicates the result of getting file size.
     * @callback yunos.cloudfs.FileDescriptor~getSizeCallback
     * @param {yunos.cloudfs.FileSystemError} error - The error object that indicates the exception
     * @param {number} size - The total space size in bytes
     * @public
     * @since 2
     */
    /**
     * Asynchronous get the file or directory total used space in bytes on disk
     * @param {yunos.cloudfs.FileDescriptor~getSizeCallback} callback - The callback when completed
     * @public
     * @since 2
     */
    public getSize(callback: (err: FileSystemError, result: number) => void): void;
    /**
     * Synchronous get the file or directory total space on disk
     * @return {number} The total space size in bytes
     * @throws {yunos.cloudfs.FileSystemError} If the file dose not exist or I/O exception
     * @public
     * @since 2
     */
    public getSizeSync(): number;
    private writeToMsg(message: Message): boolean;
    private static readFromMsg(message: Message): FileDescriptor;
    private writeToPlugin(msg: Message): boolean;
    private static readFromPlugin(msg: Message): FileDescriptor;
}
export = FileDescriptor;
