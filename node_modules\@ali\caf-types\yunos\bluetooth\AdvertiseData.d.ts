import YObject = require("../core/YObject");
/**
 * <p>Represents a Bluetooth Advertisement Data.</p>
 * <p>Advertisement Data contain additional information and advertising data.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.YObject
 * @memberof yunos.bluetooth
 * @public
 * @since 5
 *
 */
declare class AdvertiseData extends YObject {
    private advType: number;
    private manufId: number;
    private ServiceUuid: string;
    private ServiceData: Object[];
    private Uuids: string[];
    private ManufData: Object[];
    /**
     * Create a new LE AdvertiseData for registerAdvertisement.
     * @param {number} type - advertise's type
     * @param {array} uuids - uuid of this advertisement
     * @param {string} service_uuid - uuid of this service
     * @param {array} service_data - data of service
     * @param {number} manuf_id of manufacture
     * @param {array} manuf_data - data of manufacture
     * @public
     * @since 5
     *
     */
    public constructor(type: number, uuids: string[], service_uuid: string, service_data: Object[], manuf_id: number, manuf_data: Object[]);
    /**
     * The Bluetooth LE advertisement type.
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     *
     */
    public static readonly Adv_Type: {
        /**
         * advertisement type is broadcast.
         * @public
         * @since 5
         *
         */
        BROADCAST: int;
        /**
         * advertisement type is peripheral.
         * @public
         * @since 5
         *
         */
        PERIPHERAL: int;
    };
}
export = AdvertiseData;
