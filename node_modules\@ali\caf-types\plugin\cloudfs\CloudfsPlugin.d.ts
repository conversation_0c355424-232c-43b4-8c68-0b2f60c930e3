declare class CloudfsPlugin {
    constructor();
    cleanUserData(uri: string) : number;
    writeToPlugin(msg: CloudfsPlugin.Message, fd: number) : boolean;
    readFromPlugin(msg: CloudfsPlugin.Message) : number;
    
}

declare namespace CloudfsPlugin {
    export interface Message {
        writeInt32(value: number) : void;
        writeFd(value: number) : void;
        readInt32() : number;
        readFdDup(): number;
    }
}

export = CloudfsPlugin;