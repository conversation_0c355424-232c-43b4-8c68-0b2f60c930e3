import BluetoothGattCharacteristic = require("./BluetoothGattCharacteristic");
import YObject = require("../core/YObject");
/**
 * <p>Represents a Bluetooth GATT Service.</p>
 * @permission BLUETOOTH.permission.yunos.com
 * @extends yunos.core.YObject
 * @memberof yunos.bluetooth
 * @public
 * @since 3
 *
 */
declare class BluetoothGattService extends YObject {
    private _uuid;
    private _instanceId;
    private _characteristics;
    /**
     * Create a new BluetoothGattService object.
     * @param {string} uuid - the uuid of this service.
     * @public
     * @since 3
     *
     */
    public constructor(uuid: string);
    /**
     * The characteristics of this service.
     * @name yunos.bluetooth.BluetoothGattService#characteristics
     * @type {yunos.bluetooth.BluetoothGattCharacteristic[]}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly characteristics: BluetoothGattCharacteristic[];
    /**
     * Add the characteristic to this service.
     * @param {yunos.bluetooth.BluetoothGattCharacteristic} characteristic - the characteristic to
     *        be added
     * @return {number} the length of new array
     * @public
     * @since 3
     *
     */
    public addCharacteristic(characteristic: Object): void;
    /**
     * Get the characteristic with the given uuid.
     * @param {string} uuid - the uuid of the characteristic to be fetched.
     * @return {yunos.bluetooth.BluetoothGattCharacteristic} the object of Gatt Characteristic
     * @public
     * @since 3
     *
     */
    public getCharacteristic(uuid: string): BluetoothGattCharacteristic;
    /**
     * The uuid of this service
     * @name yunos.bluetooth.BluetoothGattService#uuid
     * @type {string}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly uuid: string;
    /**
     * The instance id of this Gatt service
     * @name yunos.bluetooth.BluetoothGattService#instanceId
     * @type {number}
     * @readonly
     * @public
     * @since 5
     *
     */
    public readonly instanceId: number;
    /**
     * <p>Human-readable description.</p>
     * @override
     * @public
     * @since 5
     */
    public toString(): string;
}
export = BluetoothGattService;
