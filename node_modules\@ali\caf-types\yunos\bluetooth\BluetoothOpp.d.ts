import BluetoothProfile = require("./BluetoothProfile");
import Page = require("yunos/page/Page");
import PageLink = require("yunos/page/PageLink");
import BluetoothDevice = require("./BluetoothDevice");
/**
 * @memberof yunos.bluetooth
 * @relyon YUNOS_SYSCAP_BLUETOOTH
 * @private
 */
declare class BluetoothOpp extends BluetoothProfile {
    public constructor();
    private opcInit(): number;
    private sendFiles(device: BluetoothDevice, filenames: string[]): number;
    private sendFds(device: BluetoothDevice, names: string[], fds: number[]): number;
    private stopSendingFiles(): number;
    private obexServerInit(path: string): number;
    private confirmReceivingFiles(acceptOrNot: boolean, filename: string): number;
    private stopReceivingFiles(transferId: number): number;
    private static getFileSendingPageLink(filesToSend: string[]): PageLink;
    private static showReceivedFilesInFileManager(page: Page): void;
}
export = BluetoothOpp;
