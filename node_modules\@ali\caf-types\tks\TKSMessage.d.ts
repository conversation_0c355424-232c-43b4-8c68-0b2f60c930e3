/// <reference types="node" />
import { Message } from 'ubus';
declare class TKSMessage {
    private data;
    private size;
    private pos;
    private dmsg;
    constructor(dmsg: Message, read: boolean);
    write(data: Buffer, length: number): this;
    read(length: number): Buffer;
    writeInt32(val: number): this;
    readInt32(): number;
    writeString(str: string): this;
    dataPosition(): number;
    writeByteBuffer(buffer: Buffer): this;
    readByteBuffer(): Buffer;
    writeInplace(length: number): Buffer;
    flush(): Message;
    private padSize;
    private finishWrite;
    private growData;
}
export = TKSMessage;
