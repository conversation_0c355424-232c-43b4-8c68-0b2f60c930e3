declare enum RemoteWebViewConstants {
    // Actions from Host to RemoteWebView
    ACTION_PROPERTY_SET = "property_set",
    ACTION_PROPERTY_GET = "property_get",
    ACTION_METHOD = "method",
    ACTION_METHOD_PRIVATE = "method_private",
    ACTION_METHOD_HISTORY = "method_history",
    ACTION_METHOD_PROFILE = "method_profile",
    ACTION_METHOD_SETTINGS = "method_settings",
    ACTION_CALL_RET = "call_ret",

    // Actions from RemoteWebView to Host
    ACTION_INVOKE_JS = "invoke_js",
    ACTION_INVOKE_EVENT = "invoke_event",
    ACTION_WEBVIEW_CLIENT_EVENT = "webview_client_event",
    ACTION_NEW_WEBVIEW_CREATED = "new_webview_created",

    // Actions both side used
    ACTION_JS_MSG_CALLBACK = "js_message_callback",
    ACTION_SEND_CUSTOM_MSG = "send_custom_msg",
    ACTION_VOICE = "vgui_cmd",

    BLANK_URL = "about:blank",
}

export = RemoteWebViewConstants;
