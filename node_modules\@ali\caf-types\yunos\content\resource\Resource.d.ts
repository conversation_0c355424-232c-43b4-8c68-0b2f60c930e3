import YObject = require("../../core/YObject");
import Page = require("yunos/page/Page");
/**
 * <p>Class for accessing an application's resources.</p>
 * <p>The YunOS resource system keeps track of all resources associated with an application.&nbsp;
 * You can use this class to access your application's resources.</p>
 * <p>You can generally acquire the Resources instance associated with your application with.&nbsp;
 * [Resource.getInstance()]{@link yunos.content.resource.Resource.getInstance}.</p>
 * <p>This is a standrad YunOS application project directory of res.</p>
 * <pre>
 * ├─ res
 * │  ├─ zh-CH
 * │  │  └─ string.json
 * │  │  └─ config.json
 * │  ├─ en-US
 * │  │  └─ string.json
 * │  │  └─ config.json
 * │  ├─ hdpi
 * │  │  └─ image.png
 * │  │  └─ config.json
 * │  ├─ xhdpi
 * │  │  └─ image.png
 * │  │  └─ config.json
 * </pre>
 * <p>To use a resource, you must install it correctly in the source tree (inside your project's res/ directory).&nbsp;
 * Then you can use page uri to access the resources. <br>
 * For Example, you can install two diffrent size of image resources in res/hdpi and res/xhdpi,&nbsp;
 * Resource system will choose the most suitable image by device screen sizes.</p>
 * <p>To use a language text, you must install string.json in the source tree (inside your project's res/ directory).&nbsp;
 * Then you can use [getString()]{@link yunos.content.resource.Resource#getString}&nbsp;
 * to get the correct language text according to system language setting. <br>
 * For Example, you can install two diffrent string.json in res/zh_CH and res/en_US,&nbsp;
 * both of them should have specified language key which used in application,&nbsp;
 * Resource system will choose the correct path of values.json by device language.</p>
 * <p>Methods of using config are the same of using string.</p>
 * <p>Using application resources makes it easy to update various characteristics of your application without modifying code,&nbsp;
 * by providing sets of alternative resources—enables you to optimize your application for a variety of device configurations.&nbsp;
 * This is an important aspect of developing YunOS applications that are compatible on different types of devices.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.content.resource
 * @public
 * @since 1
 */
declare class Resource extends YObject {
    private static instanceMap;
    private pageResource;
    private domainName;
    private subscheme;
    private refCount;
    private _extendPackagesPaths;
    private instanceKey;
    private configMapStore;
    /**
     * <p>Get instance of Resource by domain name.<br>
     * Use this static method to get Resource instance and do not using new Resource().</p>
     * @example
     * // You can get resources in your own application.
     * let res = Resource.getInstance();
     * let res = Resource.getInstance({subscheme:"pageA"});
     * // or use a specific domain name to get other application's resources.
     * let res = Resource.getInstance("hello.yunos.com");
     * let res = Resource.getInstance("hello.yunos.com",{subscheme:"pageA"});
     * @param {string} [domainName] - Domain's name, optional, The name of application where you can get resources from.
     * @param {Object} [options] - the options.
     * @param {string} [options.subscheme] - the subscheme of resource in page.
     * @return {yunos.content.resource.Resource} Instance of Resource of domain name.
     * @throws {TypeError} If type of domainName is not string.
     * @public
     * @since 3
     *
     */
    /**
     * <p>Get instance of Resource by domain name.<br>
     * Use this static method to get Resource instance and do not using new Resource().</p>
     * @example
     * // You can get resources in your own application.
     * let res = Resource.getInstance();
     *
     * // or use a specific domain name to get other application's resources.
     * let res = Resource.getInstance("hello.yunos.com");
     * @param {string} [domainName] - Domain's name, optional, The name of application where you can get resources from.
     * @return {yunos.content.resource.Resource} Instance of Resource of domain name.
     * @throws {TypeError} If type of domainName is not string.
     * @public
     * @since 1
     */
    public static getInstance(domainName?: string, options?: {
        subscheme?: string;
        page?: Page;
    }): Resource;
    /**
     * <p>Release instance of Resource by domain name.</p>
     * @example
     * //Get and Release instance which get own resources.
     * let res = Resource.getInstance();
     * Resource.releaseInstance();
     *
     * //Get and Release instance which get resources of application named "hello.yunos.com".
     * let res = Resource.getInstance("hello.yunos.com");
     * Resource.releaseInstance("hello.yunos.com");
     * @param {string} [domainName] Domain's name - optional, refer to the getInance's param which instance to be released.
     * @throws {TypyError} If type of domainName is not string.
     * @throws {Error} If instance is not exist.
     * @public
     * @since 1
     */
    public static releaseInstance(domainName: string): void;
    /**
     * <p>new Resource() is not Recomended,&nbsp;
     * use [Resource.getInstance()]{@link yunos.content.resource.Resource.getInstance} instead.<p>
     * @param {string} domainName - domainName of application.
     * @private
     */
    private constructor(domainName: string, options: {
        subscheme?: string;
        page?: Page;
    });
    /**
     * <p>Return the locale language text with a particular key which defined in string.json.</p>
     * @example
     * // in zh-CN, it will display "确认", in en-US, it will display "confirm".
     *
     * // res/zh-CN/string.json
     * {
     *     "confirm": "确认"
     * }
     *
     * // res/en-US/string.json
     * {
     *     "confirm": "confirm"
     * }
     *
     * // In application,
     * let res = Resource.getInstance();
     * res.getString("confirm");
     * @method yunos.content.resource.Resource#getString
     * @param {string} key - language key, The key of the language text to return from string.json.
     * @return {string} Returns the locale language text associated with the specified key,
     * or null if the key can't be found in the string.json.
     * @public
     * @since 1
     */
    /**
     * <p>Return the locale language text with a particular key which defined in string.json.</p>
     * @example
     * // in zh-CN, it will display "确认", in en-US, it will display "confirm".
     *
     * // res/zh-CN/string.json
     * {
     *     "confirm": "确认"
     * }
     *
     * // res/en-US/string.json
     * {
     *     "confirm": "confirm"
     * }
     *
     * // In application,
     * let res = Resource.getInstance();
     * res.getString("confirm");
     *
     * // Use templates,
     * // res/zh-CN/string.json
     * {
     *     "HELLO_HINT0": "Hello ${person0} and ${person1}!",
     *     "HELLO_HINT1": "Hello ${0} and ${1}!",
     *     "HELLO_HINT2": "Hello ${person0} and ${person1}!"

     * }
     * // In application, for json object,
     * res.getString("HELLO_HINT0", {
     *     "person0": "Tom",
     *     "person1": "Jerry"
     * });
     * // for array or muilty paramters, both is accepted.
     * res.getString("HELLO_HINT1", ["TOM", "Jerry"]);
     * res.getString("HELLO_HINT1", "TOM", "Jerry");
     * // for function usage, function will call for every replacement.
     * let index = 0;
     * res.getString("HELLO_HINT2", function(key) {
     *     // key is "person0" for first called and "person1" for second called.
     *     return "No." + (index++) + key;
     * });
     * @method yunos.content.resource.Resource#getString
     * @param {string} key - language key, The key of the language text to return from string.json.
     * @param {?Function|Object|string[]|...string} [options] - If language text is a template,
     * this parameter can format template, it could be funtion, json object or array, see examples
     * for more details.
     * @throws {TypeError} If Type of key is not string.
     * @throws {TypeError} If Type of return value which get from string.json by key is not string.
     * @return {string} Returns the locale language text associated with the specified key,
     * or null if the key can't be found in the string.json.
     * @public
     * @since 2
     */
    public getString(key: string, ...options: (Object | Function)[]): string;
    /**
     * <p>Return the value associated with a particular config name which defined in config.json.</p>
     * @example
     * // For diffrent highlights' style in diffrent contries.
     * // Chinese people like red color style for highlight, Japanese people prefer text bold style.
     *
     * // res/zh-CN/config.json
     * {
     *     "highlightColor": "red",
     *     "highlightFontWeight": "bold"
     * }
     *
     * // res/ja-JP/string.json
     * {
     *     "highlightColor": "#000000",
     *     "highlightFontWeight": "normal"
     * }
     *
     * // In application,
     * let res = Resource.getInstance();
     * let highlightTextView = new TextView();
     * highlightTextView.color = res.getConfig("highlightColor");
     * highlightTextView.fontWeight = res.getConfig("highlightFontWeight");
     * @param {string} key - The name of the config to return from the config file.
     * @throws {TypeError} If Type of return value which get from config.json by key is undefined.
     * @return {*} Returns the config value associated with the specified name,
     * or false if the key can't be found in the config file.
     * @public
     * @since 1
     */
    public getConfig(key: string, cached?: boolean): Object;
    /**
     * <p>Return YunOS absolute path of a raw file with file name.</p>
     * @example
     * // If application's domain name is hello.yunos.com,
     * // it will return page://hello.yunos.com/asset/book.pdf,
     * // system will find the most suitable file which in the res directory according to language and device screen sizes.
     *
     * let res = Resource.getInstance();
     * res.getRawFileSrc("book.pdf");
     * @param {string} fileName - Raw file name, should in res directory of application.
     * @return {string} YunOS absolute path of raw file, begin with "page://".
     * @public
     * @since 1
     */
    public getRawFileSrc(fileName: string): string;
    /**
     * <p>Return YunOS absolute path of a image file with file name.</p>
     * @example
     * // If application's domain name is hello.yunos.com,
     * // it will return page://hello.yunos.com/asset/image.png,
     * // system will find the most suitable file which in the res directory according to language and device screen sizes.
     *
     * let res = Resource.getInstance();
     * res.getImageSrc("image.png");
     * @param {string} fileName - Image file name, should in res directory of application.
     * @return {string} YunOS absolute path of image file, begin with "page://".
     * @public
     * @since 1
     */
    public getImageSrc(filename: string): string;
    /**
     * Return real value from tag which uesd in markup, like {string.key} {dp(20)}
     * @param {string} tag - the build in tag in system like {string.key}.
     * @return {*} return the real value through the tag.
     * @public
     * @since 3
     *
     */
    public getValueByTag(tag: string, options?: {
        [key: string]: Object;
    }): Object;
    /**
     * <p>Reset custom dimension to change resource search strategy</p>
     * @param {string} key - the key of the custom dimension
     * @param {string} value - the value of the custom dimension
     * @return {boolean} return if reset custom dimension success.
     * @public
     * @since 4
     *
     */
    public resetCustomDimension(key: string, value: string): Object;
    /**
     * <p>addExtendPackages path for markup file search</p>
     * @example
     * // before
     * <extend.widget.CustomView/>
     * // now
     * Resource.getInstance().addExtendPackages(["extend/widget"]);
     * <CustomView/>
     * @param {string[]} paths - the extend package paths
     * @public
     * @since 4
     *
     */
    public addExtendPackages(paths: string[]): void;
    private getExtendPackages(): string[];
    /**
     * Return the locale value with a particular key from the given file.
     * @param {string} key - The name of the value to return from the given file.
     * @param {string} file - name of json file, the default value is values.json
     * @return {string} Returns the locale language text associated with the specified key,
     * or null if the key can't be found in the values.json.
     * @public
     * @since 3
     *
     */
    public getValue(key: string, file?: string, cached?: boolean): Object;
    private searchFiles(filePath: string, domain: string): Object;
}
export = Resource;
